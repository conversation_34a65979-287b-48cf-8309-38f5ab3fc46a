<?php namespace app\system;

use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class DbConnection
{

   public static function connect() {
      if(Environment::isLocalhost()){
         self::$user = 'root';
         self::$pass = '';
         self::$dbname = 'visualbook';
      }

      dibi::connect([
         'driver'   => 'mysqli',
         'host'     => self::$host,
         'username' => self::$user,
         'password' => self::$pass,
         'database' => self::$dbname,
         'charset'  => 'utf8',
      ]);
   }

   private static string $host = 'localhost';
   private static string $user = 'visualbook.6';
   private static string $pass = '7W-;tqiIkHSe3XF9B';
   private static string $dbname = 'visualbook_6';
}