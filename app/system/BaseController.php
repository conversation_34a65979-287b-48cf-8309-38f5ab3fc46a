<?php namespace app\system;

use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

abstract class BaseController {
    abstract protected function getControllerMapping(): array;

    public function addToCollection(RouteCollection $collection): void {
        foreach ($this->getControllerMapping() as $path => $handler) {
            $defaults = ['_controller' => $handler];
            $requirements = [];

            if (preg_match_all('/\{(\w+)\}/', $path, $matches)) {
                foreach ($matches[1] as $param) {
                    if ($param === 'id') {
                        $requirements[$param] = '\d+';
                    }
                }
            }

            $route = new Route($path, $defaults, $requirements);
            $collection->add(md5($path), $route);
        }
    }
}