<?php namespace app\system\model\users;

use app\system\LoggedUser;
use app\front\layout\FlashMessages;
use app\system\Redirect;
use app\system\AppSession;

class LoginHelper {
    public static function loginUser(object $user, string $successMsg = 'You have been logged in!'): void {
        AppSession::start();

        $loggedUser = new LoggedUser(
            $user->id,
            $user->firstname,
            $user->lastname,
            $user->email,
            $user->role
        );

        $_SESSION['user'] = $loggedUser;
        $_SESSION['user_id'] = $user->id;
        $_SESSION['user_role'] = $user->role;
        $_SESSION['last_regeneration'] = time();

        FlashMessages::addSuccess($successMsg);
        Redirect::index();
        exit;
    }
}