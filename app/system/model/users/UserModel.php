<?php namespace app\system\model\users;

use dibi;
use Dibi\DateTime;
use <PERSON><PERSON>\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class UserModel
{

   const TABLE_NAME = 'users';
   const ID = 'id';

   /** @return Fluent|UserRow[] */
   public static function find() :Fluent|array {
      return dibi::select('*')
         ->from(self::TABLE_NAME)
         ->setupResult('setRowClass', UserRow::class);
   }

   public static function getByEmail(string $email):?UserRow {
      return self::find()
         ->where('email = %s', $email)
         ->fetch();
   }

   public static function get(int $id) :?UserRow {
//      @TODO funkční ukázaka pro získání jednoho řádku z tabulky
      return self::find()
         ->where([self::ID => $id])
         ->fetch();
   }
}