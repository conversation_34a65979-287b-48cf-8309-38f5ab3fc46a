<?php namespace app\system\model\projects;

use dibi;
use Di<PERSON>\DateTime;
use <PERSON><PERSON>\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class ProjectsModel
{

   const TABLE_NAME = 'projects';
   const ID = 'id';

   /** @return Fluent|ProjectRow[] */
   public static function find() :Fluent|array {
      return dibi::select('*')
         ->from(self::TABLE_NAME)
         ->setupResult('setRowClass', ProjectRow::class);
   }

   public static function get(int $id) :?ProjectRow {
//      @TODO funkční ukázaka pro získání jednoho řádku z tabulky
      return self::find()
         ->where([self::ID => $id])
         ->fetch();
   }

   /** @return ProjectRow[] */
   public static function getByUser(int $userId): array {
      return self::find()
      ->where('user_id = %i', $userId)
      ->fetchAll();
   }

   public static function create(string $name, string $note, int $userID): int {
      $now = new \DateTime();

      dibi::insert('projects', [
         'name' => $name,
         'note' => $note,
         'user_id' => $userID,
         'created' => new \DateTime(),
      ])->execute();

      return dibi::getInsertId();
   }

   public static function update(int $id, string $name, string $note): void {
      dibi::update('projects', [
         'name' => $name,
         'note' => $note,
      ])
      ->where('id = %i', $id)
      ->execute();
   }

   public static function delete(int $id): void {
      dibi::delete('projects')->where('id = %i', $id)->execute();
   }
}