<?php namespace app\system;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 04.08.2021 */
class UrlCache
{

   const STYLE_DIR = 'public/css/';
   const JS_DIR = 'public/js/';

   public static function appendIndex(string $url) :string {
      if(file_exists($url))
         $url .= '?'. filemtime($url);

      return $url;
   }

   public static function appendIndexCss(string $file, array $args = []) :string {
      return '<link rel="stylesheet" type="text/css" href="/' . self::appendIndex(self::STYLE_DIR . $file) . '">';
   }

   public static function appendIndexJs(string $file, array $args = []) :string {
      return '<script src="/' . self::appendIndex(self::JS_DIR . $file) . '"></script>';
   }
}