<?php namespace app\system;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class Redirect
{

   public static function to($url = '', $code = 301) :void {
      $url = $url ?: ltrim($_SERVER['REQUEST_URI'], '/');

      self::toLocation($url, $code);
   }

   public static function index() :void {
      self::to('/');
   }

   public static function login() :void {
      self::to('/login');
   }

   public static function download() :void {
      self::to('/download');
   }

   public static function self() :void {
      self::to(null);
   }

   public static function back() :void {
      self::toLocation($_SERVER['HTTP_REFERER']);
   }

   private static function toLocation($url, $code = 301) :void {
      header("Location: " . $url, true, $code);
      header("Connection: close");
      exit;
   }
}