<?php namespace app\system\component\extensions;

use Latte\Compiler\Tag;
use Latte\Essential\Nodes\PrintNode;
use Latte\Extension;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.11.2022 */
class CustomExtension extends Extension
{

   public function parse(Tag $tag, $node) {
      $tag->expectArguments();
      $node->expression = $tag->parser->parseUnquotedStringOrExpression();

      while($flag = $tag->parser->stream->tryConsume('=>', ','))
         if($flag->text == '=>'){
            $arg = array_pop($node->args);
            $node->args[$arg->value] = $tag->parser->parseExpression();
         } else $node->args[] = $tag->parser->parseExpression();

      $node->modifier = $tag->parser->parseModifier();
      $node->modifier->escape = true;

      return $node;
   }

   protected PrintNode $node;
}