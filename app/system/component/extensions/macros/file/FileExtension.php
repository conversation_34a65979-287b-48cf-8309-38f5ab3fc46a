<?php namespace app\system\component\extensions\macros\file;

use app\system\component\extensions\CustomExtension;
use app\system\component\extensions\macros\file\node\CssFileNode;
use app\system\component\extensions\macros\file\node\FileNode;
use app\system\component\extensions\macros\file\node\JsFileNode;
use La<PERSON>\Compiler\Tag;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.11.2022 */
class FileExtension extends CustomExtension
{

   public function getTags() :array {
      return [
         'cssFile' => [$this, 'parseCssFile'],
         'jsFile' => [$this, 'parseJsFile']
      ];
   }

   public function parseCssFile(Tag $tag) :FileNode {
      $this->node = new CssFileNode;
      return $this->parse($tag, $this->node);
   }

   public function parseJsFile(Tag $tag) :FileNode {
      $this->node = new JsFileNode;
      return $this->parse($tag, $this->node);
   }
}