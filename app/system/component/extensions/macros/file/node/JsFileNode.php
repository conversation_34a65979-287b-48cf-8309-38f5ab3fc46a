<?php namespace app\system\component\extensions\macros\file\node;

use Latte\Compiler\PrintContext;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.11.2022 */
class JsFileNode extends FileNode
{

   public function print(PrintContext $context) :string {
      return $context->format(
         'echo app\\system\\UrlCache::appendIndexJs(%node, %args);',
         $this->expression,
         $this->args
      );

   }
}