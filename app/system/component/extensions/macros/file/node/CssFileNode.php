<?php namespace app\system\component\extensions\macros\file\node;

use Latte\Compiler\Nodes\Php\Expression\ArrayItemNode;
use Latte\Compiler\Nodes\Php\Expression\ArrayNode;
use Latte\Compiler\Nodes\Php\Scalar\StringNode;
use Latte\Compiler\PrintContext;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.11.2022 */
class CssFileNode extends FileNode
{

   public function print(PrintContext $context) :string {
      foreach($this->args as $k => $v) {
         if($v instanceof StringNode)
            $this->args[$k] = new ArrayNode([new ArrayItemNode($v, $v)]);
      }
      return $context->format('echo app\\system\\UrlCache::appendIndexCss(%node, %args);',
         $this->expression,
         $this->args
      );
   }
}