<?php namespace app\system\component;

use app\system\Files;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.11.2021 */
abstract class Component
{

   const DEFAULT_TEMPLATE_NAME = 'template';

   abstract function setData() :array;

   public function checkPost(array $post) :void { }

   public static function getComponent(bool $cache = true) :static {
      return ComponentBuilder::get(static::class, $cache);
   }

   public static function preparePost() :void {
      self::getComponent()->checkPost($_POST);
   }

   public function prepareTemplater(string $templateName = self::DEFAULT_TEMPLATE_NAME, bool $cache = true) :Templater {
      self::preparePost();
      $this->templateName = $templateName;
      $data = $this->getData();
      return Templater::prepare($this->getTemplatePath($this->templateName), $data, $this->forcedCacheOff ? false : $cache);
   }

   public function __toString() {
      return self::getComponent()->prepareTemplater()->render();
   }

   public function echo() :void {
      self::getComponent()->prepareTemplater()->render(true);
   }

   public function renderHtml(string $templateName = self::DEFAULT_TEMPLATE_NAME, bool $cache = true) :Html {
      return $this->prepareTemplater($templateName, $this->forcedCacheOff ? false : $cache)->renderHtml();
   }

   public function addData(array $data) :static {
      $this->data = array_merge($this->data, $data);
      return $this;
   }

   public function setPath(string $path) :static {
      $this->path = $path;
      return $this;
   }

   protected function getData() :array {
      $data = $this->setData()?: [];
      $data = array_merge($data, $this->data);
      $data['_comp'] = $this;
      return $data;
   }

   protected function getTemplatePath(string $templateName) :string {
      return $this->path . '/' . $templateName . Files::LATTE_EXTENSION;
   }

   public function setTemplateName(string $templateName) :static {
      $this->templateName = $templateName;
      return $this;
   }

   protected bool $forcedCacheOff = false;
   protected string $path;
   protected array $data = array();
   private string $templateName;
}