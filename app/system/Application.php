<?php namespace app\system;

// request and response
use app\front\admin\AdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

// use of controllers
use app\front\download\DownloadController;
use app\front\index\IndexController;
use app\front\login\LoginController;
use app\front\upload\UploadController;
use app\front\projects\ProjectController;
use app\front\register\RegisterController;
use app\front\filemanager\FileManagerController;

// use other system
use app\system\AppSession;
use app\system\DbConnection;
use Tracy\Debugger;

// use routing
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Symfony\Component\Routing\Matcher\UrlMatcher;
use Symfony\Component\Routing\RequestContext;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class Application {

   private RouteCollection $routes;
    
   public static function run() :void {
      (new static())->getResponse()->send();
   }

   public function __construct() {
      AppSession::start();
      Debugger::enable(Debugger::Development);
      DbConnection::connect();
    
      $this->prepareControllers();
   }

   public function getResponse() :Response {
      //@TODO přesunout try/catch z index.php
      $request = Request::createFromGlobals();
      $context = new RequestContext();
      $context->fromRequest($request);
      $matcher = new UrlMatcher($this->routes, $context);

      try {
         // Pokus o nalezení routy
         $parameters = $matcher->match($request->getPathInfo());
         $controller = $parameters['_controller'];

         unset($parameters['_controller'], $parameters['_route']);
         foreach ($parameters as $key => $value) {
            if (str_starts_with($key, '_')) {
               unset($parameters[$key]);
            }
         }

         $response = call_user_func_array($controller, array_merge([$request], $parameters));
      } catch (ResourceNotFoundException $e) {
         //   @TODO připravit stránku 404, která bude využívat BaseLayout
         $response = new Response('TODO - 404 Not Found', 404);
      }
         return $response;
   }

   private function prepareControllers() {
      $this->registerControllerGroup(
         IndexController::class,
         UploadController::class,
         LoginController::class,
         DownloadController::class,
         RegisterController::class,
         ProjectController::class,
         AdminController::class,
         FileManagerController::class,
      );
   }

   private function registerControllerGroup(string ...$classNames) {
      foreach ($classNames as $className) {
         if (!class_exists($className)) {
            trigger_error("Class {$className} does not exist", E_USER_ERROR);
            continue;
         }

         /** @var BaseController $instance */
         $instance = new $className();
         $instance->addToCollection($this->getRoutesCollection());
      }
   }

   private function getRoutesCollection(): RouteCollection {
      return $this->routes ??= new RouteCollection();
   }

        /*$this->routes->add('index', new Route(
           '/',
           ['_controller' => [new IndexController(), 'index']]
        ));*/
        
        // 1) Zaregistrování jednotlivých controllerů a jejich cest
        /*$this->routes->add('login', new Route(
           '/login',
           ['_controller' => [new LoginController(), 'index']]
        ));*/

        /*$this->routes->add('upload', new Route(
           '/upload',
           ['_controller' => [new UploadController(), 'index']]
        ));*/

        /*$this->routes->add('download', new Route(
           '/download',
           ['_controller' => [new DownloadController(), 'index']]
        ));*/

        /*$this->routes->add('register', new Route(
           '/register',
           ['_controller' => [new RegisterController(), 'index']]
        ));*/

        //if(AppSession::getLoggedID()) - @TODO registrace controlleru pouze pokud jsem přihlášený
        /*$this->routes->add('project', new Route(
           '/project/{id}',
           ['_controller' => [new ProjectController(), 'detail']],
           ['id' => '\\d+']
        ));*/

        /*$this->routes->add('projects', new Route(
           '/projects',
           ['_controller' => [new ProjectController(), 'index']],
        ));*/
}