<?php namespace app\system;

class LoggedUser {
    public readonly int $id;
    public readonly string $firstname;
    public readonly string $lastname;
    public readonly string $email;
    public readonly string $role;

    public function __construct(int $id, string $firstname, string $lastname, string $email, string $role) {
        $this->id = $id;
        $this->firstname = $firstname;
        $this->lastname = $lastname;
        $this->email = $email;
        $this->role = $role;
    }

    public function getGreetName(): string {
        return $this->firstname;
    }
}