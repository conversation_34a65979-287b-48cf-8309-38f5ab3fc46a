<?php namespace app\system;

use app\system\model\projects\ProjectRow;
use app\system\model\users\UserRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class AppSession
{

   public static function start() :void {
      if(self::$started)
         return;

      ini_set('session.use_only_cookies', 1);
      ini_set('session.use_strict_mode', 1);

      session_set_cookie_params([
         'lifetime' => 1800,
         //'domain' => 'kvmediastudio.cz',
         'path' => '/',
         'secure' => true,
         'httponly' => true
      ]);

      session_start();
      self::$started = true;
      
      if (isset($_SESSION["user_id"])) {
         if (!isset($_SESSION["last_regeneration"])) {
            self::regenerateSessionIdLoggedIn();
         } else {
            $interval = 60 * 30;
         
            if (time() - $_SESSION["last_regeneration"] >= $interval) {
               self::regenerateSessionIdLoggedIn();
            }
         }
      } else {
         if (!isset($_SESSION["last_regeneration"])) {
            self::regenerateSessionId();
         } else {
            $interval = 60 * 30;
         
            if (time() - $_SESSION["last_regeneration"] >= $interval) {
               self::regenerateSessionId();
            }
         }
      } 
   }

   private static function regenerateSessionId() {
      session_regenerate_id(true);
      $_SESSION["last_regeneration"] = time();
   }

   private static function regenerateSessionIdLoggedIn() {
      session_regenerate_id(true);

      $userId = $_SESSION["user_id"];
      $newSessionId = session_create_id();
      $sessionId = $newSessionId . "_" . $userId;
      session_id($sessionId);

      $_SESSION["last_regeneration"] = time();
   }
   
   public static function getLoggedID() :?int {
      return intval($_SESSION["user_id"] ?? 0) ?: null;
   }

//   @TODO připravit tak aby getUser vracel třídu, např. LoggedUser
   public static function getUser() :?LoggedUser {
      $user = $_SESSION['user'] ?? null;
      return $user instanceof LoggedUser ? $user : null;  
   }

   public static function getUserName() :?string {
      $user = self::getUser();

      return $user ? $user->getGreetName(): null;
   }

   protected static bool $started = false;
}