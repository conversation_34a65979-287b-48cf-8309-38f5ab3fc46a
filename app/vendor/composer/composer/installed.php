<?php return array(
    'root' => array(
        'name' => 'kvmedia/visual-book',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '5fb2d844aae95c25a01e37ee72921eef0c418a25',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'dg/dibi' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dibi/dibi' => array(
            'pretty_version' => 'v5.0.2',
            'version' => '5.0.2.0',
            'reference' => '97053089e05eadab4c368ad37619adf411b2847e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dibi/dibi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kvmedia/visual-book' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '5fb2d844aae95c25a01e37ee72921eef0c418a25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'latte/latte' => array(
            'pretty_version' => 'v3.0.20',
            'version' => '3.0.20.0',
            'reference' => '4db7a5502f8cef02fffa84fc9c34a635d9c79d4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../latte/latte',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/http' => array(
            'pretty_version' => 'v3.3.2',
            'version' => '3.3.2.0',
            'reference' => '3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/http',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ce708655043c7050eb050df361c5e313cf708309',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'studio-42/elfinder' => array(
            'pretty_version' => '2.1.65',
            'version' => '2.1.65.0',
            'reference' => '5535a8677558c44a20c19ff9b97ec37702f9c44d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../studio-42/elfinder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => '6023ec7607254c87c5e69fb3558255aca440d72b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'ee9a67edc6baa33e5fae662f94f91fd262930996',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tracy/tracy' => array(
            'pretty_version' => 'v2.10.10',
            'version' => '2.10.10.0',
            'reference' => '32303e02c222eea8571402a8310fc3fe70422c37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tracy/tracy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
