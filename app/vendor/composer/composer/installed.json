{"packages": [{"name": "dibi/dibi", "version": "v5.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "97053089e05eadab4c368ad37619adf411b2847e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/97053089e05eadab4c368ad37619adf411b2847e", "reference": "97053089e05eadab4c368ad37619adf411b2847e", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "replace": {"dg/dibi": "*"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "nette/di": "^3.1", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "time": "2024-09-03T01:18:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "support": {"issues": "https://github.com/dg/dibi/issues", "source": "https://github.com/dg/dibi/tree/v5.0.2"}, "install-path": "../dibi/dibi"}, {"name": "latte/latte", "version": "v3.0.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/application": "<3.1.7", "nette/caching": "<3.1.4"}, "require-dev": {"nette/php-generator": "^4.0", "nette/tester": "^2.5", "nette/utils": "^4.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.10"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-intl": "to use Latte\\Engine::setLocale()", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "time": "2024-10-08T00:58:27+00:00", "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v3.0.20"}, "install-path": "../latte/latte"}, {"name": "nette/http", "version": "v3.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "shasum": ""}, "require": {"nette/utils": "^4.0.4", "php": "8.1 - 8.4"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect MIME type of uploaded files by Nette\\Http\\FileUpload", "ext-gd": "to use image function in Nette\\Http\\FileUpload", "ext-intl": "to support punycode by Nette\\Http\\Url", "ext-session": "to use Nette\\Http\\Session"}, "time": "2025-01-12T16:27:57+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.3.2"}, "install-path": "../nette/http"}, {"name": "nette/utils", "version": "v4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "ce708655043c7050eb050df361c5e313cf708309"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/ce708655043c7050eb050df361c5e313cf708309", "reference": "ce708655043c7050eb050df361c5e313cf708309", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "time": "2025-03-30T21:06:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.6"}, "install-path": "../nette/utils"}, {"name": "studio-42/elfinder", "version": "2.1.65", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Studio-42/elFinder.git", "reference": "5535a8677558c44a20c19ff9b97ec37702f9c44d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Studio-42/elFinder/zipball/5535a8677558c44a20c19ff9b97ec37702f9c44d", "reference": "5535a8677558c44a20c19ff9b97ec37702f9c44d", "shasum": ""}, "require": {"php": ">=5.2"}, "suggest": {"barryvdh/elfinder-flysystem-driver": "VolumeDriver for elFinder to use Flysystem as a root.", "google/apiclient": "VolumeDriver GoogleDrive require `google/apiclient:^2.0.", "kunalvarma05/dropbox-php-sdk": "VolumeDriver `Dropbox`2 require `kunalvarma05/dropbox-php-sdk.", "nao-pon/flysystem-google-drive": "require in GoogleDrive network volume mounting with Flysystem."}, "time": "2024-01-05T05:01:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "Troex Nevelin", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://xoops.hypweb.net"}, {"name": "Community contributions", "homepage": "https://github.com/Studio-42/elFinder/contributors"}], "description": "File manager for web", "homepage": "http://elfinder.org", "support": {"issues": "https://github.com/Studio-42/elFinder/issues", "source": "https://github.com/Studio-42/elFinder/tree/2.1.65"}, "funding": [{"url": "https://github.com/nao-pon", "type": "github"}], "install-path": "../studio-42/elfinder"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/http-foundation", "version": "v7.2.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "6023ec7607254c87c5e69fb3558255aca440d72b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6023ec7607254c87c5e69fb3558255aca440d72b", "reference": "6023ec7607254c87c5e69fb3558255aca440d72b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "time": "2025-04-09T08:14:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php83"}, {"name": "symfony/routing", "version": "v7.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/ee9a67edc6baa33e5fae662f94f91fd262930996", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "time": "2025-01-17T10:56:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/routing"}, {"name": "tracy/tracy", "version": "v2.10.10", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "32303e02c222eea8571402a8310fc3fe70422c37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/32303e02c222eea8571402a8310fc3fe70422c37", "reference": "32303e02c222eea8571402a8310fc3fe70422c37", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5 || ^3.0", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/tester": "^2.2", "nette/utils": "^3.0 || ^4.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "time": "2025-04-28T14:35:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.10-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.10.10"}, "install-path": "../tracy/tracy"}], "dev": true, "dev-package-names": []}