<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname(dirname(dirname($vendorDir)));

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DateError' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateError.php',
    'DateException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateException.php',
    'DateInvalidOperationException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateInvalidOperationException.php',
    'DateInvalidTimeZoneException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateInvalidTimeZoneException.php',
    'DateMalformedIntervalStringException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateMalformedIntervalStringException.php',
    'DateMalformedPeriodStringException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateMalformedPeriodStringException.php',
    'DateMalformedStringException' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateMalformedStringException.php',
    'DateObjectError' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateObjectError.php',
    'DateRangeError' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/DateRangeError.php',
    'Dibi\\Bridges\\Nette\\DibiExtension22' => $vendorDir . '/dibi/dibi/src/Dibi/Bridges/Nette/DibiExtension22.php',
    'Dibi\\Bridges\\Nette\\DibiExtension3' => $vendorDir . '/dibi/dibi/src/Dibi/Bridges/Nette/DibiExtension3.php',
    'Dibi\\Bridges\\Tracy\\Panel' => $vendorDir . '/dibi/dibi/src/Dibi/Bridges/Tracy/Panel.php',
    'Dibi\\Connection' => $vendorDir . '/dibi/dibi/src/Dibi/Connection.php',
    'Dibi\\ConstraintViolationException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\DataSource' => $vendorDir . '/dibi/dibi/src/Dibi/DataSource.php',
    'Dibi\\DateTime' => $vendorDir . '/dibi/dibi/src/Dibi/DateTime.php',
    'Dibi\\Driver' => $vendorDir . '/dibi/dibi/src/Dibi/interfaces.php',
    'Dibi\\DriverException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\Drivers\\DummyDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/DummyDriver.php',
    'Dibi\\Drivers\\FirebirdDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/FirebirdDriver.php',
    'Dibi\\Drivers\\FirebirdReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/FirebirdReflector.php',
    'Dibi\\Drivers\\FirebirdResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/FirebirdResult.php',
    'Dibi\\Drivers\\MySqlReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/MySqlReflector.php',
    'Dibi\\Drivers\\MySqliDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/MySqliDriver.php',
    'Dibi\\Drivers\\MySqliResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/MySqliResult.php',
    'Dibi\\Drivers\\NoDataResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/NoDataResult.php',
    'Dibi\\Drivers\\OdbcDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OdbcDriver.php',
    'Dibi\\Drivers\\OdbcReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OdbcReflector.php',
    'Dibi\\Drivers\\OdbcResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OdbcResult.php',
    'Dibi\\Drivers\\OracleDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OracleDriver.php',
    'Dibi\\Drivers\\OracleReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OracleReflector.php',
    'Dibi\\Drivers\\OracleResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/OracleResult.php',
    'Dibi\\Drivers\\PdoDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/PdoDriver.php',
    'Dibi\\Drivers\\PdoResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/PdoResult.php',
    'Dibi\\Drivers\\PostgreDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/PostgreDriver.php',
    'Dibi\\Drivers\\PostgreReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/PostgreReflector.php',
    'Dibi\\Drivers\\PostgreResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/PostgreResult.php',
    'Dibi\\Drivers\\Sqlite3Driver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/Sqlite3Driver.php',
    'Dibi\\Drivers\\Sqlite3Result' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/Sqlite3Result.php',
    'Dibi\\Drivers\\SqliteDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqliteDriver.php',
    'Dibi\\Drivers\\SqliteReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqliteReflector.php',
    'Dibi\\Drivers\\SqliteResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqliteResult.php',
    'Dibi\\Drivers\\SqlsrvDriver' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqlsrvDriver.php',
    'Dibi\\Drivers\\SqlsrvReflector' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqlsrvReflector.php',
    'Dibi\\Drivers\\SqlsrvResult' => $vendorDir . '/dibi/dibi/src/Dibi/Drivers/SqlsrvResult.php',
    'Dibi\\Event' => $vendorDir . '/dibi/dibi/src/Dibi/Event.php',
    'Dibi\\Exception' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\Expression' => $vendorDir . '/dibi/dibi/src/Dibi/Expression.php',
    'Dibi\\Fluent' => $vendorDir . '/dibi/dibi/src/Dibi/Fluent.php',
    'Dibi\\ForeignKeyConstraintViolationException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\HashMap' => $vendorDir . '/dibi/dibi/src/Dibi/HashMap.php',
    'Dibi\\HashMapBase' => $vendorDir . '/dibi/dibi/src/Dibi/HashMap.php',
    'Dibi\\Helpers' => $vendorDir . '/dibi/dibi/src/Dibi/Helpers.php',
    'Dibi\\IConnection' => $vendorDir . '/dibi/dibi/src/Dibi/interfaces.php',
    'Dibi\\IDataSource' => $vendorDir . '/dibi/dibi/src/Dibi/interfaces.php',
    'Dibi\\Literal' => $vendorDir . '/dibi/dibi/src/Dibi/Literal.php',
    'Dibi\\Loggers\\FileLogger' => $vendorDir . '/dibi/dibi/src/Dibi/Loggers/FileLogger.php',
    'Dibi\\NotImplementedException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\NotNullConstraintViolationException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\NotSupportedException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\PcreException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\ProcedureException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Dibi\\Reflection\\Column' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/Column.php',
    'Dibi\\Reflection\\Database' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/Database.php',
    'Dibi\\Reflection\\ForeignKey' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/ForeignKey.php',
    'Dibi\\Reflection\\Index' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/Index.php',
    'Dibi\\Reflection\\Result' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/Result.php',
    'Dibi\\Reflection\\Table' => $vendorDir . '/dibi/dibi/src/Dibi/Reflection/Table.php',
    'Dibi\\Reflector' => $vendorDir . '/dibi/dibi/src/Dibi/interfaces.php',
    'Dibi\\Result' => $vendorDir . '/dibi/dibi/src/Dibi/Result.php',
    'Dibi\\ResultDriver' => $vendorDir . '/dibi/dibi/src/Dibi/interfaces.php',
    'Dibi\\ResultIterator' => $vendorDir . '/dibi/dibi/src/Dibi/ResultIterator.php',
    'Dibi\\Row' => $vendorDir . '/dibi/dibi/src/Dibi/Row.php',
    'Dibi\\Translator' => $vendorDir . '/dibi/dibi/src/Dibi/Translator.php',
    'Dibi\\Type' => $vendorDir . '/dibi/dibi/src/Dibi/Type.php',
    'Dibi\\UniqueConstraintViolationException' => $vendorDir . '/dibi/dibi/src/Dibi/exceptions.php',
    'Latte\\Attributes\\TemplateFilter' => $vendorDir . '/latte/latte/src/Latte/attributes.php',
    'Latte\\Attributes\\TemplateFunction' => $vendorDir . '/latte/latte/src/Latte/attributes.php',
    'Latte\\Bridges\\Tracy\\BlueScreenPanel' => $vendorDir . '/latte/latte/src/Bridges/Tracy/BlueScreenPanel.php',
    'Latte\\Bridges\\Tracy\\LattePanel' => $vendorDir . '/latte/latte/src/Bridges/Tracy/LattePanel.php',
    'Latte\\Bridges\\Tracy\\TracyExtension' => $vendorDir . '/latte/latte/src/Bridges/Tracy/TracyExtension.php',
    'Latte\\CompileException' => $vendorDir . '/latte/latte/src/Latte/exceptions.php',
    'Latte\\Compiler\\Block' => $vendorDir . '/latte/latte/src/Latte/Compiler/Block.php',
    'Latte\\Compiler\\Escaper' => $vendorDir . '/latte/latte/src/Latte/Compiler/Escaper.php',
    'Latte\\Compiler\\ExpressionBuilder' => $vendorDir . '/latte/latte/src/Latte/Compiler/ExpressionBuilder.php',
    'Latte\\Compiler\\Node' => $vendorDir . '/latte/latte/src/Latte/Compiler/Node.php',
    'Latte\\Compiler\\NodeHelpers' => $vendorDir . '/latte/latte/src/Latte/Compiler/NodeHelpers.php',
    'Latte\\Compiler\\NodeTraverser' => $vendorDir . '/latte/latte/src/Latte/Compiler/NodeTraverser.php',
    'Latte\\Compiler\\Nodes\\AreaNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/AreaNode.php',
    'Latte\\Compiler\\Nodes\\AuxiliaryNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/AuxiliaryNode.php',
    'Latte\\Compiler\\Nodes\\FragmentNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/FragmentNode.php',
    'Latte\\Compiler\\Nodes\\Html\\AttributeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Html/AttributeNode.php',
    'Latte\\Compiler\\Nodes\\Html\\BogusTagNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Html/BogusTagNode.php',
    'Latte\\Compiler\\Nodes\\Html\\CommentNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Html/CommentNode.php',
    'Latte\\Compiler\\Nodes\\Html\\ElementNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Html/ElementNode.php',
    'Latte\\Compiler\\Nodes\\NopNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/NopNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ArgumentNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ArgumentNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ArrayItemNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ArrayItemNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ClosureUseNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ClosureUseNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ComplexTypeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ComplexTypeNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ExpressionNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ExpressionNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ArrayAccessNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ArrayAccessNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ArrayItemNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ArrayItemNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ArrayNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ArrayNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\AssignNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/AssignNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\AssignOpNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/AssignOpNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\AuxiliaryNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/AuxiliaryNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\BinaryOpNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/BinaryOpNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\CastNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/CastNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ClassConstantFetchNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ClassConstantFetchNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\CloneNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/CloneNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ClosureNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ClosureNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ConstantFetchNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ConstantFetchNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\EmptyNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/EmptyNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\ErrorSuppressNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/ErrorSuppressNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\FilterCallNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/FilterCallNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\FunctionCallNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/FunctionCallNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\FunctionCallableNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/FunctionCallableNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\InNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/InNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\InstanceofNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/InstanceofNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\IssetNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/IssetNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\MatchNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/MatchNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\MethodCallNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/MethodCallNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\MethodCallableNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/MethodCallableNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\NewNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/NewNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\NotNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/NotNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\PostOpNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/PostOpNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\PreOpNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/PreOpNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\PropertyFetchNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/PropertyFetchNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\StaticCallNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/StaticMethodCallNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\StaticCallableNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/StaticMethodCallableNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\StaticMethodCallNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/StaticMethodCallNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\StaticMethodCallableNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/StaticMethodCallableNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\StaticPropertyFetchNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/StaticPropertyFetchNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\TemporaryNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/TemporaryNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\TernaryNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/TernaryNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\UnaryOpNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/UnaryOpNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Expression\\VariableNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Expression/VariableNode.php',
    'Latte\\Compiler\\Nodes\\Php\\FilterNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/FilterNode.php',
    'Latte\\Compiler\\Nodes\\Php\\IdentifierNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/IdentifierNode.php',
    'Latte\\Compiler\\Nodes\\Php\\InterpolatedStringPartNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/InterpolatedStringPartNode.php',
    'Latte\\Compiler\\Nodes\\Php\\IntersectionTypeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/IntersectionTypeNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ListItemNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ListItemNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ListNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ListNode.php',
    'Latte\\Compiler\\Nodes\\Php\\MatchArmNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/MatchArmNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ModifierNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ModifierNode.php',
    'Latte\\Compiler\\Nodes\\Php\\NameNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/NameNode.php',
    'Latte\\Compiler\\Nodes\\Php\\NullableTypeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/NullableTypeNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ParameterNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ParameterNode.php',
    'Latte\\Compiler\\Nodes\\Php\\ScalarNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/ScalarNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\BooleanNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/BooleanNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\FloatNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/FloatNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\IntegerNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/IntegerNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\InterpolatedStringNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/InterpolatedStringNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\NullNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/NullNode.php',
    'Latte\\Compiler\\Nodes\\Php\\Scalar\\StringNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/Scalar/StringNode.php',
    'Latte\\Compiler\\Nodes\\Php\\SuperiorTypeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/SuperiorTypeNode.php',
    'Latte\\Compiler\\Nodes\\Php\\UnionTypeNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/UnionTypeNode.php',
    'Latte\\Compiler\\Nodes\\Php\\VarLikeIdentifierNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/Php/VarLikeIdentifierNode.php',
    'Latte\\Compiler\\Nodes\\StatementNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/StatementNode.php',
    'Latte\\Compiler\\Nodes\\TemplateNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/TemplateNode.php',
    'Latte\\Compiler\\Nodes\\TextNode' => $vendorDir . '/latte/latte/src/Latte/Compiler/Nodes/TextNode.php',
    'Latte\\Compiler\\PhpHelpers' => $vendorDir . '/latte/latte/src/Latte/Compiler/PhpHelpers.php',
    'Latte\\Compiler\\Position' => $vendorDir . '/latte/latte/src/Latte/Compiler/Position.php',
    'Latte\\Compiler\\PrintContext' => $vendorDir . '/latte/latte/src/Latte/Compiler/PrintContext.php',
    'Latte\\Compiler\\Tag' => $vendorDir . '/latte/latte/src/Latte/Compiler/Tag.php',
    'Latte\\Compiler\\TagLexer' => $vendorDir . '/latte/latte/src/Latte/Compiler/TagLexer.php',
    'Latte\\Compiler\\TagParser' => $vendorDir . '/latte/latte/src/Latte/Compiler/TagParser.php',
    'Latte\\Compiler\\TagParserData' => $vendorDir . '/latte/latte/src/Latte/Compiler/TagParserData.php',
    'Latte\\Compiler\\TemplateGenerator' => $vendorDir . '/latte/latte/src/Latte/Compiler/TemplateGenerator.php',
    'Latte\\Compiler\\TemplateLexer' => $vendorDir . '/latte/latte/src/Latte/Compiler/TemplateLexer.php',
    'Latte\\Compiler\\TemplateParser' => $vendorDir . '/latte/latte/src/Latte/Compiler/TemplateParser.php',
    'Latte\\Compiler\\TemplateParserHtml' => $vendorDir . '/latte/latte/src/Latte/Compiler/TemplateParserHtml.php',
    'Latte\\Compiler\\Token' => $vendorDir . '/latte/latte/src/Latte/Compiler/Token.php',
    'Latte\\Compiler\\TokenStream' => $vendorDir . '/latte/latte/src/Latte/Compiler/TokenStream.php',
    'Latte\\ContentType' => $vendorDir . '/latte/latte/src/Latte/ContentType.php',
    'Latte\\Engine' => $vendorDir . '/latte/latte/src/Latte/Engine.php',
    'Latte\\Essential\\AuxiliaryIterator' => $vendorDir . '/latte/latte/src/Latte/Essential/AuxiliaryIterator.php',
    'Latte\\Essential\\Blueprint' => $vendorDir . '/latte/latte/src/Latte/Essential/Blueprint.php',
    'Latte\\Essential\\CachingIterator' => $vendorDir . '/latte/latte/src/Latte/Essential/CachingIterator.php',
    'Latte\\Essential\\CoreExtension' => $vendorDir . '/latte/latte/src/Latte/Essential/CoreExtension.php',
    'Latte\\Essential\\Filters' => $vendorDir . '/latte/latte/src/Latte/Essential/Filters.php',
    'Latte\\Essential\\Nodes\\BlockNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/BlockNode.php',
    'Latte\\Essential\\Nodes\\CaptureNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/CaptureNode.php',
    'Latte\\Essential\\Nodes\\ContentTypeNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ContentTypeNode.php',
    'Latte\\Essential\\Nodes\\DebugbreakNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/DebugbreakNode.php',
    'Latte\\Essential\\Nodes\\DefineNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/DefineNode.php',
    'Latte\\Essential\\Nodes\\DoNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/DoNode.php',
    'Latte\\Essential\\Nodes\\DumpNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/DumpNode.php',
    'Latte\\Essential\\Nodes\\EmbedNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/EmbedNode.php',
    'Latte\\Essential\\Nodes\\ExtendsNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ExtendsNode.php',
    'Latte\\Essential\\Nodes\\FirstLastSepNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/FirstLastSepNode.php',
    'Latte\\Essential\\Nodes\\ForNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ForNode.php',
    'Latte\\Essential\\Nodes\\ForeachNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ForeachNode.php',
    'Latte\\Essential\\Nodes\\IfChangedNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IfChangedNode.php',
    'Latte\\Essential\\Nodes\\IfContentNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IfContentNode.php',
    'Latte\\Essential\\Nodes\\IfNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IfNode.php',
    'Latte\\Essential\\Nodes\\ImportNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ImportNode.php',
    'Latte\\Essential\\Nodes\\IncludeBlockNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IncludeBlockNode.php',
    'Latte\\Essential\\Nodes\\IncludeFileNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IncludeFileNode.php',
    'Latte\\Essential\\Nodes\\IterateWhileNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/IterateWhileNode.php',
    'Latte\\Essential\\Nodes\\JumpNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/JumpNode.php',
    'Latte\\Essential\\Nodes\\NAttrNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/NAttrNode.php',
    'Latte\\Essential\\Nodes\\NClassNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/NClassNode.php',
    'Latte\\Essential\\Nodes\\NElseNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/NElseNode.php',
    'Latte\\Essential\\Nodes\\NTagNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/NTagNode.php',
    'Latte\\Essential\\Nodes\\ParametersNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/ParametersNode.php',
    'Latte\\Essential\\Nodes\\PrintNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/PrintNode.php',
    'Latte\\Essential\\Nodes\\RawPhpNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/RawPhpNode.php',
    'Latte\\Essential\\Nodes\\RollbackNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/RollbackNode.php',
    'Latte\\Essential\\Nodes\\SpacelessNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/SpacelessNode.php',
    'Latte\\Essential\\Nodes\\SwitchNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/SwitchNode.php',
    'Latte\\Essential\\Nodes\\TemplatePrintNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/TemplatePrintNode.php',
    'Latte\\Essential\\Nodes\\TemplateTypeNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/TemplateTypeNode.php',
    'Latte\\Essential\\Nodes\\TraceNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/TraceNode.php',
    'Latte\\Essential\\Nodes\\TranslateNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/TranslateNode.php',
    'Latte\\Essential\\Nodes\\TryNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/TryNode.php',
    'Latte\\Essential\\Nodes\\VarNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/VarNode.php',
    'Latte\\Essential\\Nodes\\VarPrintNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/VarPrintNode.php',
    'Latte\\Essential\\Nodes\\VarTypeNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/VarTypeNode.php',
    'Latte\\Essential\\Nodes\\WhileNode' => $vendorDir . '/latte/latte/src/Latte/Essential/Nodes/WhileNode.php',
    'Latte\\Essential\\Passes' => $vendorDir . '/latte/latte/src/Latte/Essential/Passes.php',
    'Latte\\Essential\\RawPhpExtension' => $vendorDir . '/latte/latte/src/Latte/Essential/RawPhpExtension.php',
    'Latte\\Essential\\RollbackException' => $vendorDir . '/latte/latte/src/Latte/Essential/RollbackException.php',
    'Latte\\Essential\\Tracer' => $vendorDir . '/latte/latte/src/Latte/Essential/Tracer.php',
    'Latte\\Essential\\TranslatorExtension' => $vendorDir . '/latte/latte/src/Latte/Essential/TranslatorExtension.php',
    'Latte\\Exception' => $vendorDir . '/latte/latte/src/Latte/exceptions.php',
    'Latte\\Extension' => $vendorDir . '/latte/latte/src/Latte/Extension.php',
    'Latte\\Helpers' => $vendorDir . '/latte/latte/src/Latte/Helpers.php',
    'Latte\\Loader' => $vendorDir . '/latte/latte/src/Latte/Loader.php',
    'Latte\\Loaders\\FileLoader' => $vendorDir . '/latte/latte/src/Latte/Loaders/FileLoader.php',
    'Latte\\Loaders\\StringLoader' => $vendorDir . '/latte/latte/src/Latte/Loaders/StringLoader.php',
    'Latte\\Policy' => $vendorDir . '/latte/latte/src/Latte/Policy.php',
    'Latte\\PositionAwareException' => $vendorDir . '/latte/latte/src/Latte/PositionAwareException.php',
    'Latte\\RuntimeException' => $vendorDir . '/latte/latte/src/Latte/exceptions.php',
    'Latte\\Runtime\\Block' => $vendorDir . '/latte/latte/src/Latte/Runtime/Block.php',
    'Latte\\Runtime\\FilterExecutor' => $vendorDir . '/latte/latte/src/Latte/Runtime/FilterExecutor.php',
    'Latte\\Runtime\\FilterInfo' => $vendorDir . '/latte/latte/src/Latte/Runtime/FilterInfo.php',
    'Latte\\Runtime\\Filters' => $vendorDir . '/latte/latte/src/Latte/Runtime/Filters.php',
    'Latte\\Runtime\\FunctionExecutor' => $vendorDir . '/latte/latte/src/Latte/Runtime/FunctionExecutor.php',
    'Latte\\Runtime\\Html' => $vendorDir . '/latte/latte/src/Latte/Runtime/Html.php',
    'Latte\\Runtime\\HtmlStringable' => $vendorDir . '/latte/latte/src/Latte/Runtime/HtmlStringable.php',
    'Latte\\Runtime\\Template' => $vendorDir . '/latte/latte/src/Latte/Runtime/Template.php',
    'Latte\\Sandbox\\Nodes\\FunctionCallNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/FunctionCallNode.php',
    'Latte\\Sandbox\\Nodes\\FunctionCallableNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/FunctionCallableNode.php',
    'Latte\\Sandbox\\Nodes\\MethodCallNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/MethodCallNode.php',
    'Latte\\Sandbox\\Nodes\\MethodCallableNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/MethodCallableNode.php',
    'Latte\\Sandbox\\Nodes\\PropertyFetchNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/PropertyFetchNode.php',
    'Latte\\Sandbox\\Nodes\\SandboxNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/SandboxNode.php',
    'Latte\\Sandbox\\Nodes\\StaticMethodCallNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/StaticMethodCallNode.php',
    'Latte\\Sandbox\\Nodes\\StaticMethodCallableNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/StaticMethodCallableNode.php',
    'Latte\\Sandbox\\Nodes\\StaticPropertyFetchNode' => $vendorDir . '/latte/latte/src/Latte/Sandbox/Nodes/StaticPropertyFetchNode.php',
    'Latte\\Sandbox\\RuntimeChecker' => $vendorDir . '/latte/latte/src/Latte/Sandbox/RuntimeChecker.php',
    'Latte\\Sandbox\\SandboxExtension' => $vendorDir . '/latte/latte/src/Latte/Sandbox/SandboxExtension.php',
    'Latte\\Sandbox\\SecurityPolicy' => $vendorDir . '/latte/latte/src/Latte/Sandbox/SecurityPolicy.php',
    'Latte\\SecurityViolationException' => $vendorDir . '/latte/latte/src/Latte/exceptions.php',
    'Latte\\Tools\\Linter' => $vendorDir . '/latte/latte/src/Tools/Linter.php',
    'Nette\\ArgumentOutOfRangeException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Bridges\\HttpDI\\HttpExtension' => $vendorDir . '/nette/http/src/Bridges/HttpDI/HttpExtension.php',
    'Nette\\Bridges\\HttpDI\\SessionExtension' => $vendorDir . '/nette/http/src/Bridges/HttpDI/SessionExtension.php',
    'Nette\\Bridges\\HttpTracy\\SessionPanel' => $vendorDir . '/nette/http/src/Bridges/HttpTracy/SessionPanel.php',
    'Nette\\DeprecatedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\DirectoryNotFoundException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\FileNotFoundException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\HtmlStringable' => $vendorDir . '/nette/utils/src/HtmlStringable.php',
    'Nette\\Http\\Context' => $vendorDir . '/nette/http/src/Http/Context.php',
    'Nette\\Http\\FileUpload' => $vendorDir . '/nette/http/src/Http/FileUpload.php',
    'Nette\\Http\\Helpers' => $vendorDir . '/nette/http/src/Http/Helpers.php',
    'Nette\\Http\\IRequest' => $vendorDir . '/nette/http/src/Http/IRequest.php',
    'Nette\\Http\\IResponse' => $vendorDir . '/nette/http/src/Http/IResponse.php',
    'Nette\\Http\\Request' => $vendorDir . '/nette/http/src/Http/Request.php',
    'Nette\\Http\\RequestFactory' => $vendorDir . '/nette/http/src/Http/RequestFactory.php',
    'Nette\\Http\\Response' => $vendorDir . '/nette/http/src/Http/Response.php',
    'Nette\\Http\\Session' => $vendorDir . '/nette/http/src/Http/Session.php',
    'Nette\\Http\\SessionSection' => $vendorDir . '/nette/http/src/Http/SessionSection.php',
    'Nette\\Http\\Url' => $vendorDir . '/nette/http/src/Http/Url.php',
    'Nette\\Http\\UrlImmutable' => $vendorDir . '/nette/http/src/Http/UrlImmutable.php',
    'Nette\\Http\\UrlScript' => $vendorDir . '/nette/http/src/Http/UrlScript.php',
    'Nette\\Http\\UserStorage' => $vendorDir . '/nette/http/src/Http/UserStorage.php',
    'Nette\\IOException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\InvalidArgumentException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\InvalidStateException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Iterators\\CachingIterator' => $vendorDir . '/nette/utils/src/Iterators/CachingIterator.php',
    'Nette\\Iterators\\Mapper' => $vendorDir . '/nette/utils/src/Iterators/Mapper.php',
    'Nette\\Localization\\ITranslator' => $vendorDir . '/nette/utils/src/compatibility.php',
    'Nette\\Localization\\Translator' => $vendorDir . '/nette/utils/src/Translator.php',
    'Nette\\MemberAccessException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\NotImplementedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\NotSupportedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\OutOfRangeException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\ShouldNotHappenException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\SmartObject' => $vendorDir . '/nette/utils/src/SmartObject.php',
    'Nette\\StaticClass' => $vendorDir . '/nette/utils/src/StaticClass.php',
    'Nette\\UnexpectedValueException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Utils\\ArrayHash' => $vendorDir . '/nette/utils/src/Utils/ArrayHash.php',
    'Nette\\Utils\\ArrayList' => $vendorDir . '/nette/utils/src/Utils/ArrayList.php',
    'Nette\\Utils\\Arrays' => $vendorDir . '/nette/utils/src/Utils/Arrays.php',
    'Nette\\Utils\\AssertionException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Callback' => $vendorDir . '/nette/utils/src/Utils/Callback.php',
    'Nette\\Utils\\DateTime' => $vendorDir . '/nette/utils/src/Utils/DateTime.php',
    'Nette\\Utils\\FileInfo' => $vendorDir . '/nette/utils/src/Utils/FileInfo.php',
    'Nette\\Utils\\FileSystem' => $vendorDir . '/nette/utils/src/Utils/FileSystem.php',
    'Nette\\Utils\\Finder' => $vendorDir . '/nette/utils/src/Utils/Finder.php',
    'Nette\\Utils\\Floats' => $vendorDir . '/nette/utils/src/Utils/Floats.php',
    'Nette\\Utils\\Helpers' => $vendorDir . '/nette/utils/src/Utils/Helpers.php',
    'Nette\\Utils\\Html' => $vendorDir . '/nette/utils/src/Utils/Html.php',
    'Nette\\Utils\\IHtmlString' => $vendorDir . '/nette/utils/src/compatibility.php',
    'Nette\\Utils\\Image' => $vendorDir . '/nette/utils/src/Utils/Image.php',
    'Nette\\Utils\\ImageColor' => $vendorDir . '/nette/utils/src/Utils/ImageColor.php',
    'Nette\\Utils\\ImageException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\ImageType' => $vendorDir . '/nette/utils/src/Utils/ImageType.php',
    'Nette\\Utils\\Iterables' => $vendorDir . '/nette/utils/src/Utils/Iterables.php',
    'Nette\\Utils\\Json' => $vendorDir . '/nette/utils/src/Utils/Json.php',
    'Nette\\Utils\\JsonException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\ObjectHelpers' => $vendorDir . '/nette/utils/src/Utils/ObjectHelpers.php',
    'Nette\\Utils\\Paginator' => $vendorDir . '/nette/utils/src/Utils/Paginator.php',
    'Nette\\Utils\\Random' => $vendorDir . '/nette/utils/src/Utils/Random.php',
    'Nette\\Utils\\Reflection' => $vendorDir . '/nette/utils/src/Utils/Reflection.php',
    'Nette\\Utils\\ReflectionMethod' => $vendorDir . '/nette/utils/src/Utils/ReflectionMethod.php',
    'Nette\\Utils\\RegexpException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Strings' => $vendorDir . '/nette/utils/src/Utils/Strings.php',
    'Nette\\Utils\\Type' => $vendorDir . '/nette/utils/src/Utils/Type.php',
    'Nette\\Utils\\UnknownImageFileException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Validators' => $vendorDir . '/nette/utils/src/Utils/Validators.php',
    'Override' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/Override.php',
    'RecursiveCallbackFilterIterator' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeLocalFileSystem.class.php',
    'SQLite3Exception' => $vendorDir . '/symfony/polyfill-php83/Resources/stubs/SQLite3Exception.php',
    'Tracy\\Bar' => $vendorDir . '/tracy/tracy/src/Tracy/Bar/Bar.php',
    'Tracy\\BlueScreen' => $vendorDir . '/tracy/tracy/src/Tracy/BlueScreen/BlueScreen.php',
    'Tracy\\Bridges\\Nette\\Bridge' => $vendorDir . '/tracy/tracy/src/Bridges/Nette/Bridge.php',
    'Tracy\\Bridges\\Nette\\MailSender' => $vendorDir . '/tracy/tracy/src/Bridges/Nette/MailSender.php',
    'Tracy\\Bridges\\Nette\\TracyExtension' => $vendorDir . '/tracy/tracy/src/Bridges/Nette/TracyExtension.php',
    'Tracy\\Bridges\\Psr\\PsrToTracyLoggerAdapter' => $vendorDir . '/tracy/tracy/src/Bridges/Psr/PsrToTracyLoggerAdapter.php',
    'Tracy\\Bridges\\Psr\\TracyToPsrLoggerAdapter' => $vendorDir . '/tracy/tracy/src/Bridges/Psr/TracyToPsrLoggerAdapter.php',
    'Tracy\\CodeHighlighter' => $vendorDir . '/tracy/tracy/src/Tracy/BlueScreen/CodeHighlighter.php',
    'Tracy\\Debugger' => $vendorDir . '/tracy/tracy/src/Tracy/Debugger/Debugger.php',
    'Tracy\\DefaultBarPanel' => $vendorDir . '/tracy/tracy/src/Tracy/Bar/DefaultBarPanel.php',
    'Tracy\\DeferredContent' => $vendorDir . '/tracy/tracy/src/Tracy/Debugger/DeferredContent.php',
    'Tracy\\DevelopmentStrategy' => $vendorDir . '/tracy/tracy/src/Tracy/Debugger/DevelopmentStrategy.php',
    'Tracy\\Dumper' => $vendorDir . '/tracy/tracy/src/Tracy/Dumper/Dumper.php',
    'Tracy\\Dumper\\Describer' => $vendorDir . '/tracy/tracy/src/Tracy/Dumper/Describer.php',
    'Tracy\\Dumper\\Exposer' => $vendorDir . '/tracy/tracy/src/Tracy/Dumper/Exposer.php',
    'Tracy\\Dumper\\Renderer' => $vendorDir . '/tracy/tracy/src/Tracy/Dumper/Renderer.php',
    'Tracy\\Dumper\\Value' => $vendorDir . '/tracy/tracy/src/Tracy/Dumper/Value.php',
    'Tracy\\FileSession' => $vendorDir . '/tracy/tracy/src/Tracy/Session/FileSession.php',
    'Tracy\\Helpers' => $vendorDir . '/tracy/tracy/src/Tracy/Helpers.php',
    'Tracy\\IBarPanel' => $vendorDir . '/tracy/tracy/src/Tracy/Bar/IBarPanel.php',
    'Tracy\\ILogger' => $vendorDir . '/tracy/tracy/src/Tracy/Logger/ILogger.php',
    'Tracy\\Logger' => $vendorDir . '/tracy/tracy/src/Tracy/Logger/Logger.php',
    'Tracy\\NativeSession' => $vendorDir . '/tracy/tracy/src/Tracy/Session/NativeSession.php',
    'Tracy\\OutputDebugger' => $vendorDir . '/tracy/tracy/src/Tracy/OutputDebugger/OutputDebugger.php',
    'Tracy\\ProductionStrategy' => $vendorDir . '/tracy/tracy/src/Tracy/Debugger/ProductionStrategy.php',
    'Tracy\\SessionStorage' => $vendorDir . '/tracy/tracy/src/Tracy/Session/SessionStorage.php',
    'dibi' => $vendorDir . '/dibi/dibi/src/Dibi/dibi.php',
    'elFinder' => $vendorDir . '/studio-42/elfinder/php/elFinder.class.php',
    'elFinderAbortException' => $vendorDir . '/studio-42/elfinder/php/elFinder.class.php',
    'elFinderConnector' => $vendorDir . '/studio-42/elfinder/php/elFinderConnector.class.php',
    'elFinderEditor' => $vendorDir . '/studio-42/elfinder/php/editors/editor.php',
    'elFinderEditorOnlineConvert' => $vendorDir . '/studio-42/elfinder/php/editors/OnlineConvert/editor.php',
    'elFinderEditorZipArchive' => $vendorDir . '/studio-42/elfinder/php/editors/ZipArchive/editor.php',
    'elFinderEditorZohoOffice' => $vendorDir . '/studio-42/elfinder/php/editors/ZohoOffice/editor.php',
    'elFinderLibGdBmp' => $vendorDir . '/studio-42/elfinder/php/libs/GdBmp.php',
    'elFinderPlugin' => $vendorDir . '/studio-42/elfinder/php/elFinderPlugin.php',
    'elFinderPluginAutoResize' => $vendorDir . '/studio-42/elfinder/php/plugins/AutoResize/plugin.php',
    'elFinderPluginAutoRotate' => $vendorDir . '/studio-42/elfinder/php/plugins/AutoRotate/plugin.php',
    'elFinderPluginNormalizer' => $vendorDir . '/studio-42/elfinder/php/plugins/Normalizer/plugin.php',
    'elFinderPluginSanitizer' => $vendorDir . '/studio-42/elfinder/php/plugins/Sanitizer/plugin.php',
    'elFinderPluginWatermark' => $vendorDir . '/studio-42/elfinder/php/plugins/Watermark/plugin.php',
    'elFinderPluginWinRemoveTailDots' => $vendorDir . '/studio-42/elfinder/php/plugins/WinRemoveTailDots/plugin.php',
    'elFinderSession' => $vendorDir . '/studio-42/elfinder/php/elFinderSession.php',
    'elFinderSessionInterface' => $vendorDir . '/studio-42/elfinder/php/elFinderSessionInterface.php',
    'elFinderTriggerException' => $vendorDir . '/studio-42/elfinder/php/elFinder.class.php',
    'elFinderVolumeBox' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeBox.class.php',
    'elFinderVolumeDriver' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeDriver.class.php',
    'elFinderVolumeDropbox' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeDropbox.class.php',
    'elFinderVolumeDropbox2' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeDropbox2.class.php',
    'elFinderVolumeFTP' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeFTP.class.php',
    'elFinderVolumeFlysystemGoogleDriveCache' => $vendorDir . '/studio-42/elfinder/php/elFinderFlysystemGoogleDriveNetmount.php',
    'elFinderVolumeFlysystemGoogleDriveNetmount' => $vendorDir . '/studio-42/elfinder/php/elFinderFlysystemGoogleDriveNetmount.php',
    'elFinderVolumeGoogleDrive' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeGoogleDrive.class.php',
    'elFinderVolumeGroup' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeGroup.class.php',
    'elFinderVolumeLocalFileSystem' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeLocalFileSystem.class.php',
    'elFinderVolumeMySQL' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeMySQL.class.php',
    'elFinderVolumeOneDrive' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeOneDrive.class.php',
    'elFinderVolumeSFTPphpseclib' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeSFTPphpseclib.class.php',
    'elFinderVolumeTrash' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeTrash.class.php',
    'elFinderVolumeTrashMySQL' => $vendorDir . '/studio-42/elfinder/php/elFinderVolumeTrashMySQL.class.php',
);
