!function(e,t){if("function"==typeof define&&define.amd)define(["elfinder"],e);else if(t){var i=t.prototype._options.commandsOptions.edit.editors;t.prototype._options.commandsOptions.edit.editors=i.concat(e(t))}}(function(e){"use strict";var t,i=window.location.search.match(/getfile=([a-z]+)/),n=e.prototype.hasRequire,o={bmp:"image/x-ms-bmp",dng:"image/x-adobe-dng",gif:"image/gif",jpeg:"image/jpeg",jpg:"image/jpeg",pdf:"application/pdf",png:"image/png",ppm:"image/x-portable-pixmap",psd:"image/vnd.adobe.photoshop",pxd:"image/x-pixlr-data",svg:"image/svg+xml",tiff:"image/tiff",webp:"image/webp",xcf:"image/x-xcf",sketch:"application/x-sketch",ico:"image/x-icon",dds:"image/vnd-ms.dds",emf:"application/x-msmetafile"},a=function(e,i,n){t||(t=i.arrayFlip(o));var a=t[e]||i.mimeTypes[e];return n?"jpg"===a&&(a="jpeg"):"jpeg"===a&&(a="jpg"),a},r=function(e,t){var i=$.Deferred();try{var n=document.createElement("canvas"),o=n.getContext("2d"),a=new Image,r=function(){var e,o,a=n.toDataURL(t);e=(o=a.match(/^data:([a-z0-9]+\/[a-z0-9.+-]+)/i))?o[1]:"",e.toLowerCase()===t.toLowerCase()?i.resolve(n.toDataURL(t),n):i.reject()};return a.src=e,$(a).on("load",function(){try{n.width=a.width,n.height=a.height,o.drawImage(a,0,0),r()}catch(e){i.reject()}}).on("error",function(){i.reject()}),i}catch(s){return i.reject()}},s=function(e,t,i,n){var o,r=$(this).children("img:first").data("ext",a(t.mime,n)),s=$('<div class="elfinder-edit-spinner elfinder-edit-image"></div>').html('<span class="elfinder-spinner-text">'+n.i18n("ntfloadimg")+'</span><span class="elfinder-spinner"></span>').hide().appendTo(this),d=function(){r.attr("id",e+"-img").attr("src",o||i).css({height:"","max-width":"100%","max-height":"100%",cursor:"pointer"}).data("loading",function(e){var t=r.closest(".elfinder-dialog").find("button,.elfinder-titlebar-button");return t.prop("disabled",!e)[e?"removeClass":"addClass"]("ui-state-disabled"),r.css("opacity",e?"":"0.3"),s[e?"hide":"show"](),r})};i.match(/^data:/)?d():n.openUrl(t.hash,!1,function(e){o=e,r.attr("_src",i),d()})},d=function(e,t){var i,n,o,a=e.attr("style");try{e.attr("style",""),i=e.get(0),n=document.createElement("canvas"),n.width=i.width,n.height=i.height,e.attr("style",a),n.getContext("2d").drawImage(i,0,0),o=n.toDataURL(t)}catch(r){o=e.attr("src")}return o},c=function(e){var t,i=$(e),n=$.Deferred().always(function(){i.off("load",r)}),o="about:blank",a=function(){t=setTimeout(function(){var e;try{e=base.contentWindow.location.href}catch(t){e=null}e===o?n.resolve():--s>0?a():n.reject()},500)},r=function(){t&&clearTimeout(t),n.resolve()},s=20;return i.one("load",r),e.src=o,a(),n};return i&&(i=i[1],"ckeditor"===i&&(e.prototype._options.getFileCallback=function(e,t){window.opener.CKEDITOR.tools.callFunction(function(){var e=new RegExp("(?:[?&]|&amp;)CKEditorFuncNum=([^&]+)","i"),t=window.location.search.match(e);return t&&t.length>1?t[1]:""}(),t.convAbsUrl(e.url)),t.destroy(),window.close()})),[{info:{id:"tuiimgedit",name:"TUI Image Editor",iconImg:"img/editor-icons.png 0 -48",dataScheme:!0,schemeContent:!0,openMaximized:!0,canMakeEmpty:!1,integrate:{title:"TOAST UI Image Editor",link:"http://ui.toast.com/tui-image-editor/"}},mimes:["image/jpeg","image/png","image/gif","image/svg+xml","image/x-ms-bmp"],html:'<div class="elfinder-edit-imageeditor"><canvas></canvas></div>',setup:function(e,t){t.UA.ltIE8||t.UA.Mobile?this.disabled=!0:(this.opts=Object.assign({version:"v3.15.3"},e.extraOptions.tuiImgEditOpts||{},{iconsPath:t.baseUrl+"img/tui-",theme:{}}),t.isSameOrigin(this.opts.iconsPath)||(this.disabled=!0,t.debug("warning","Setting `commandOptions.edit.extraOptions.tuiImgEditOpts.iconsPath` MUST follow the same origin policy.")))},init:function(e,t,i,n){this.data("url",i)},load:function(e){var t,i=this,n=this.fm,o=$.Deferred(),a=n.options.cdns,r=i.confObj.opts.version,s=function(t){var a,r,s,d,c,l=$(e),p=l.parent(),m=i.confObj.opts,f=(m.iconsPath,$('<div class="tui-image-editor-container">').appendTo(p)),u=[$('<div class="tui-image-editor-submenu"></div>').appendTo(f),$('<div class="tui-image-editor-controls"></div>').appendTo(f)],g=new t(e,{includeUI:{loadImage:{path:l.data("url"),name:i.file.name},theme:m.theme,initMenu:"filter",menuBarPosition:"bottom"},cssMaxWidth:Math.max(300,p.width()),cssMaxHeight:Math.max(200,p.height()-(u[0].height()+u[1].height()+3)),usageStatistics:!1}),h=l.find("canvas:first").get(0),v=function(e){if("undefined"!=typeof e){var t,i,n,o=$(h),r=parseInt(o.attr("width")),s=parseInt(o.attr("height")),d=r/s;0===e?(i=r,n=s):(i=parseInt(o.css("max-width"))+Number(e),n=i/d,i>r&&n>s&&(i=r,n=s)),t=Math.round(i/r*100),t<100?(g.resetZoom(),g.stopDrawingMode(),a.hide()):a.show(),y.text(t+"%"),g.resizeCanvasDimension({width:i,height:n}),c&&setTimeout(function(){c&&v(e)},50)}},b=$('<span class="ui-icon ui-icon-plusthick"></span>').data("val",10),x=$('<span class="ui-icon ui-icon-minusthick"></span>').data("val",-10),y=$("<button></button>").css("width","4em").text("%").attr("title","100%").data("val",0);f.remove(),l.removeData("url").data("mime",i.file.mime),"image/jpeg"===i.file.mime?(l.data("quality",n.storage("jpgQuality")||n.option("jpgQuality")),r=$('<input type="number" class="ui-corner-all elfinder-resize-quality elfinder-tabstop"/>').attr("min","1").attr("max","100").attr("title","1 - 100").on("change",function(){var e=r.val();l.data("quality",e),s&&cancelAnimationFrame(s),s=requestAnimationFrame(function(){h.toBlob(function(e){e&&r.next("span").text(" ("+n.formatSize(e.size)+")")},"image/jpeg",Math.max(Math.min(e,100),1)/100)})}).val(l.data("quality")),$('<div class="ui-dialog-buttonset elfinder-edit-extras elfinder-edit-extras-quality"></div>').append($("<span>").html(n.i18n("quality")+" : "),r,$("<span></span>")).prependTo(l.parent().next())):"image/svg+xml"===i.file.mime&&l.closest(".ui-dialog").trigger("changeType",{extention:"png",mime:"image/png",keepEditor:!0}),$('<div class="ui-dialog-buttonset elfinder-edit-extras"></div>').append(x,y,b).attr("title",n.i18n("scale")).on("click","span,button",function(){v($(this).data("val"))}).on("mousedown mouseup mouseleave","span",function(e){c=!1,d&&clearTimeout(d),"mousedown"===e.type&&(d=setTimeout(function(){c=!0,v($(e.target).data("val"))},500))}).prependTo(l.parent().next()),setTimeout(function(){o.resolve(g),r&&(r.trigger("change"),g.on("redoStackChanged undoStackChanged",function(){r.trigger("change")})),a=l.find(".tie-btn-zoomIn,.tie-btn-zoomOut,.tie-btn-hand"),v(null)},100),l.find(".tui-colorpicker-palette-container").on("click",".tui-colorpicker-palette-preview",function(){$(this).closest(".color-picker-control").height("auto").find(".tui-colorpicker-slider-container").toggle()}),l.on("click",function(){l.find(".tui-colorpicker-slider-container").hide()})};return i.confObj.editor?s(i.confObj.editor):(t=$.Deferred(),n.loadCss([a.tui+"/tui-color-picker/latest/tui-color-picker.css",a.tui+"/tui-image-editor/"+r+"/tui-image-editor.css"]),n.hasRequire?(require.config({paths:{"fabric/dist/fabric.require":a.fabric+"/fabric.require.min",fabric:a.fabric+"/fabric.min","tui-code-snippet":a.tui+"/tui.code-snippet/latest/tui-code-snippet.min","tui-color-picker":a.tui+"/tui-color-picker/latest/tui-color-picker.min","tui-image-editor":a.tui+"/tui-image-editor/"+r+"/tui-image-editor.min"}}),require(["tui-image-editor"],function(e){t.resolve(e)})):n.loadScript([a.fabric+"/fabric.min.js",a.tui+"/tui.code-snippet/latest/tui-code-snippet.min.js"],function(){n.loadScript([a.tui+"/tui-color-picker/latest/tui-color-picker.min.js"],function(){n.loadScript([a.tui+"/tui-image-editor/"+r+"/tui-image-editor.min.js"],function(){t.resolve(window.tui.ImageEditor)},{loadType:"tag"})},{loadType:"tag"})},{loadType:"tag"}),t.done(function(e){i.confObj.editor=e,s(e)})),o},getContent:function(e){var t=this.editor,i=t.fm,n=$(e),o=n.data("quality");if(t.instance)return"image/jpeg"===n.data("mime")&&(o=o||i.storage("jpgQuality")||i.option("jpgQuality"),o=Math.max(.1,Math.min(1,o/100))),t.instance.toDataURL({format:a(n.data("mime"),i,!0),quality:o})},save:function(e){var t,i=$(e),n=i.data("quality"),o=i.data("hash");this.instance.deactivateAll(),"undefined"!=typeof n&&this.fm.storage("jpgQuality",n),o&&(t=this.fm.file(o),i.data("mime",t.mime))}},{info:{id:"photopea",name:"Photopea",iconImg:"img/editor-icons.png 0 -160",single:!0,noContent:!0,arrayBufferContent:!0,openMaximized:!0,canMakeEmpty:["image/jpeg","image/png","image/gif","image/svg+xml","image/x-ms-bmp","image/tiff","image/webp","image/vnd.adobe.photoshop","application/pdf","image/x-portable-pixmap","image/x-sketch","image/x-icon","image/vnd-ms.dds"],integrate:{title:"Photopea",link:"https://www.photopea.com/learn/"}},mimes:["image/jpeg","image/png","image/gif","image/svg+xml","image/x-ms-bmp","image/tiff","image/x-adobe-dng","image/webp","image/x-xcf","image/vnd.adobe.photoshop","application/pdf","image/x-portable-pixmap","image/x-sketch","image/x-icon","image/vnd-ms.dds","application/x-msmetafile"],html:'<iframe style="width:100%;height:100%;border:none;"></iframe>',setup:function(e,t){(t.UA.IE||t.UA.Mobile)&&(this.disabled=!0)},init:function(e,t,i,n){var r,s,d,c="https://www.photopea.com",l=$(this).hide().on("load",function(){l.show()}).on("error",function(){f.remove(),l.show()}),p=this.editor,m=p.confObj,f=$('<div class="elfinder-edit-spinner elfinder-edit-photopea"></div>').html('<span class="elfinder-spinner-text">'+n.i18n("nowLoading")+'</span><span class="elfinder-spinner"></span>').appendTo(l.parent()),u=n.arrayFlip(m.info.canMakeEmpty),g=function(e){var t=a(e,n),i=o[t];return m.mimesFlip[i]?"jpeg"===t&&(t="jpg"):t="",t&&u[i]||(t="psd",i=o[t],l.closest(".ui-dialog").trigger("changeType",{extention:t,mime:i,keepEditor:!0})),t},h=t.mime;m.mimesFlip||(m.mimesFlip=n.arrayFlip(m.mimes,!0)),m.liveMsg||(m.liveMsg=function(e,t,i){var o,a=e.get(0).contentWindow,r=0,s=null,d=$.Deferred().done(function(){t.remove(),r=1,a.postMessage(s,c)});this.load=function(){return n.getContents(i.hash,"arraybuffer").done(function(e){s=e})},this.receive=function(t){var i=t.originalEvent;i.origin===c&&i.source===a&&("done"===i.data?0===r?d.resolve():1===r?(r=2,e.trigger("contentsloaded")):o&&"pending"===o.state()&&o.reject("errDataEmpty"):"Save"===i.data?p.doSave():o&&"pending"===o.state()&&("object"==typeof i.data?o.resolve("data:"+h+";base64,"+n.arrayBufferToBase64(i.data)):o.reject("errDataEmpty")))},this.getContent=function(){var t,i;if(r>1)return o&&"pending"===o.state()&&o.reject(),o=null,o=$.Deferred(),2===r?(r=3,o.resolve("data:"+h+";base64,"+n.arrayBufferToBase64(s)),s=null,o):(e.data("mime")&&(h=e.data("mime"),t=g(h)),(i=e.data("quality"))&&(t+=":"+i/100),a.postMessage('app.activeDocument.saveToOE("'+t+'")',c),o)}}),l.parent().css("padding",0),s=g(t.mime),r=p.liveMsg=new m.liveMsg(l,f,t),$(window).on("message."+n.namespace,r.receive),r.load().done(function(){var e=JSON.stringify({files:[],environment:{lang:n.lang.replace(/_/g,"-"),customIO:{save:'app.echoToOE("Save");'}}});l.attr("src",c+"/#"+encodeURI(e))}).fail(function(e){e&&n.error(e),p.initFail=!0}),"image/jpeg"!==t.mime&&"image/webp"!==t.mime||(l.data("quality",n.storage("jpgQuality")||n.option("jpgQuality")),d=$('<input type="number" class="ui-corner-all elfinder-resize-quality elfinder-tabstop"/>').attr("min","1").attr("max","100").attr("title","1 - 100").on("change",function(){var e=d.val();l.data("quality",e)}).val(l.data("quality")),$('<div class="ui-dialog-buttonset elfinder-edit-extras elfinder-edit-extras-quality"></div>').append($("<span>").html(n.i18n("quality")+" : "),d,$("<span></span>")).prependTo(l.parent().next()))},load:function(e){var t=$.Deferred(),i=this,n=(this.fm,$(e));return i.initFail?t.reject():n.on("contentsloaded",function(){t.resolve(i.liveMsg)}),t},getContent:function(){return this.editor.liveMsg?this.editor.liveMsg.getContent():void 0},save:function(e,t){var i,n=$(e),o=n.data("quality"),a=n.data("hash");"undefined"!=typeof o&&this.fm.storage("jpgQuality",o),a?(i=this.fm.file(a),n.data("mime",i.mime)):n.removeData("mime")},close:function(e,t){$(e).attr("src",""),t&&$(window).off("message."+this.fm.namespace,t.receive)}},{info:{id:"pixo",name:"Pixo Editor",iconImg:"img/editor-icons.png 0 -208",dataScheme:!0,schemeContent:!0,single:!0,canMakeEmpty:!1,integrate:{title:"Pixo Editor",link:"https://pixoeditor.com/privacy-policy/"}},mimes:["image/jpeg","image/png","image/gif","image/svg+xml","image/x-ms-bmp"],html:'<div class="elfinder-edit-imageeditor"><img/></div>',setup:function(e,t){!t.UA.ltIE8&&e.extraOptions&&e.extraOptions.pixo&&e.extraOptions.pixo.apikey?this.editorOpts=e.extraOptions.pixo:this.disabled=!0},init:function(e,t,i,n){s.call(this,e,t,i,n)},getContent:function(){return $(this).children("img:first").attr("src")},load:function(e){var t,i,n,o,s,d=this,c=this.fm,l=$(e),p=l.children("img:first"),m=l.closest(".ui-dialog"),f=c.getUI(),u=$.Deferred(),g=$("#elfinder-pixo-container"),h=function(n){var h;g.length?g.appendTo(g.parent()):(g=$('<div id="elfinder-pixo-container" class="ui-front"></div>').css({position:"fixed",top:0,right:0,width:"100%",height:$(window).height(),overflow:"hidden"}).hide().appendTo(f.hasClass("elfinder-fullscreen")?f:"body"),f.on("resize."+c.namespace,function(e,t){e.preventDefault(),e.stopPropagation(),t&&t.fullscreen&&g.appendTo("on"===t.fullscreen?f:"body")}),c.bind("destroy",function(){s&&s.cancelEditing(),g.remove()})),p.on("click",v),h=Object.assign({type:"child",parent:g.get(0),output:{format:"png"},onSave:function(n){var s=n.toBlob().type,l=a(s,c),f=function(e){p.one("load error",function(){p.data("loading")&&p.data("loading")(!0)}).attr("crossorigin","anonymous").attr("src",e)},u=n.toDataURL();p.data("loading")(),delete e._canvas,p.data("ext")!==l?r(u,d.file.mime).done(function(n,a){a&&(e._canvas=o=a,i.trigger("change"),t&&t.show()),f(n)}).fail(function(){m.trigger("changeType",{extention:l,mime:s}),f(u)}):f(u)},onClose:function(){m.removeClass(c.res("class","preventback")),c.toggleMaximize(g,!1),g.hide(),c.toFront(m)}},d.confObj.editorOpts),d.trigger("Prepare",{node:e,editorObj:Pixo,instance:void 0,opts:h}),s=new Pixo.Bridge(h),u.resolve(s),l.on("saveAsFail",v),n&&n()},v=function(){m.addClass(c.res("class","preventback")),c.toggleMaximize(g,!0),c.toFront(g),g.show().data("curhash",d.file.hash),s.edit(p.get(0)),p.data("loading")(!0)};return p.data("loading")(),"image/jpeg"===d.file.mime&&(i=$('<input type="number" class="ui-corner-all elfinder-resize-quality elfinder-tabstop"/>').attr("min","1").attr("max","100").attr("title","1 - 100").on("change",function(){var e=i.val();n&&cancelAnimationFrame(n),n=requestAnimationFrame(function(){o&&o.toBlob(function(e){e&&i.next("span").text(" ("+c.formatSize(e.size)+")")},"image/jpeg",Math.max(Math.min(e,100),1)/100)})}).val(c.storage("jpgQuality")||c.option("jpgQuality")),t=$('<div class="ui-dialog-buttonset elfinder-edit-extras elfinder-edit-extras-quality"></div>').hide().append($("<span>").html(c.i18n("quality")+" : "),i,$("<span></span>")).prependTo(l.parent().next()),l.data("quty",i)),"undefined"==typeof Pixo?c.loadScript(["https://pixoeditor.com:8443/editor/scripts/bridge.m.js"],function(){h(v)},{loadType:"tag"}):(h(),v()),u},save:function(e){var t,i=this,n=$(e),o=n.children("img:first");e._canvas?(n.data("quty")&&(t=n.data("quty").val(),t&&this.fm.storage("jpgQuality",t)),o.attr("src",e._canvas.toDataURL(i.file.mime,t?Math.max(Math.min(t,100),1)/100:void 0))):"data:"!==o.attr("src").substr(0,5)&&o.attr("src",d(o,this.file.mime))},close:function(e,t){t&&t.destroy()}},{setup:function(e,t){!t.UA.ltIE8&&t.options.cdns.ace||(this.disabled=!0)},info:{id:"aceeditor",name:"ACE Editor",iconImg:"img/editor-icons.png 0 -96"},load:function(e){var t=this,i=this.fm,n=$.Deferred(),o=i.options.cdns.ace,a=function(){var i,a,r,s=$(e),d=s.parent(),c=d.parent(),l=e.id+"_ace",p=(t.file.name.replace(/^.+\.([^.]+)|(.+)$/,"$1$2").toLowerCase(),{"text/x-php":"php","application/x-php":"php","text/html":"html","application/xhtml+xml":"html","text/javascript":"javascript","application/javascript":"javascript","text/css":"css","text/x-c":"c_cpp","text/x-csrc":"c_cpp","text/x-chdr":"c_cpp","text/x-c++":"c_cpp","text/x-c++src":"c_cpp","text/x-c++hdr":"c_cpp","text/x-shellscript":"sh","application/x-csh":"sh","text/x-python":"python","text/x-java":"java","text/x-java-source":"java","text/x-ruby":"ruby","text/x-perl":"perl","application/x-perl":"perl","text/x-sql":"sql","text/xml":"xml","application/docbook+xml":"xml","application/xml":"xml"});d.height(d.height()),ace.config.set("basePath",o),a=$('<div id="'+l+'" style="width:100%; height:100%;"></div>').text(s.val()).insertBefore(s.hide()),s.data("ace",!0),i=ace.edit(l),i.$blockScrolling=1/0,i.setOptions({theme:"ace/theme/monokai",fontSize:"14px",wrap:!0}),ace.config.loadModule("ace/ext/modelist",function(){r=ace.require("ace/ext/modelist").getModeForPath("/"+t.file.name).name,"text"===r&&p[t.file.mime]&&(r=p[t.file.mime]),d.prev().children(".elfinder-dialog-title").append(" ("+t.file.mime+" : "+r.split(/[\/\\]/).pop()+")"),i.setOptions({mode:"ace/mode/"+r}),"resolved"===n.state()&&c.trigger("resize")}),ace.config.loadModule("ace/ext/language_tools",function(){ace.require("ace/ext/language_tools"),i.setOptions({enableBasicAutocompletion:!0,enableSnippets:!0,enableLiveAutocompletion:!1})}),ace.config.loadModule("ace/ext/settings_menu",function(){ace.require("ace/ext/settings_menu").init(i)}),i.commands.addCommand({name:"saveFile",bindKey:{win:"Ctrl-s",mac:"Command-s"},exec:function(e){t.doSave()}}),i.commands.addCommand({name:"closeEditor",bindKey:{win:"Ctrl-w|Ctrl-q",mac:"Command-w|Command-q"},exec:function(e){t.doCancel()}}),i.resize(),$('<div class="ui-dialog-buttonset"></div>').css("float","left").append($("<button></button>").html(t.fm.i18n("TextArea")).button().on("click",function(){s.data("ace")?(s.removeData("ace"),a.hide(),s.val(i.session.getValue()).show().trigger("focus"),$(this).text("AceEditor")):(s.data("ace",!0),a.show(),i.setValue(s.hide().val(),-1),i.focus(),$(this).html(t.fm.i18n("TextArea")))})).append($("<button>Ace editor setting</button>").button({icons:{primary:"ui-icon-gear",secondary:"ui-icon-triangle-1-e"},text:!1}).on("click",function(){i.showSettingsMenu(),$("#ace_settingsmenu").css("font-size","80%").find('div[contains="setOptions"]').hide().end().parent().appendTo($("#elfinder"))})).prependTo(d.next()),t.trigger("Prepare",{node:e,editorObj:ace,instance:i,opts:{}}),n.resolve(i)};return t.confObj.loader||(t.confObj.loader=$.Deferred(),t.fm.loadScript([o+"/ace.js"],function(){t.confObj.loader.resolve()},void 0,{obj:window,name:"ace"})),t.confObj.loader.done(a),n},close:function(e,t){t&&t.destroy()},save:function(e,t){t&&$(e).data("ace")&&(e.value=t.session.getValue())},focus:function(e,t){t&&$(e).data("ace")&&t.focus()},resize:function(e,t,i,n){t&&t.resize()}},{setup:function(e,t){!t.UA.ltIE10&&t.options.cdns.codemirror||(this.disabled=!0)},info:{id:"codemirror",name:"CodeMirror",iconImg:"img/editor-icons.png 0 -176"},load:function(e){var t=this.fm,i=t.convAbsUrl(t.options.cdns.codemirror),o=$.Deferred(),a=this,r=function(t){var r,s,d,c=$(e),l=c.parent();l.height(l.height()),d={lineNumbers:!0,lineWrapping:!0,extraKeys:{"Ctrl-S":function(){a.doSave()},"Ctrl-Q":function(){a.doCancel()},"Ctrl-W":function(){a.doCancel()}}},a.trigger("Prepare",{node:e,editorObj:t,instance:void 0,opts:d}),r=t.fromTextArea(e,d),o.resolve(r);var p,m,f,u;p||(p=t.findModeByMIME(a.file.mime)),!p&&(m=a.file.name.match(/.+\.([^.]+)$/))&&(p=t.findModeByExtension(m[1])),p&&(t.modeURL=n?"codemirror/mode/%N/%N.min":i+"/mode/%N/%N.min.js",f=p.mode,u=p.mime,r.setOption("mode",u),t.autoLoadMode(r,f),l.prev().children(".elfinder-dialog-title").append(" ("+u+("null"!=f?" : "+f:"")+")")),s=$(r.getWrapperElement()).css({padding:0,border:"none"}),c.data("cm",!0),s.height("100%"),$('<div class="ui-dialog-buttonset"></div>').css("float","left").append($("<button></button>").html(a.fm.i18n("TextArea")).button().on("click",function(){c.data("cm")?(c.removeData("cm"),s.hide(),c.val(r.getValue()).show().trigger("focus"),$(this).text("CodeMirror")):(c.data("cm",!0),s.show(),r.setValue(c.hide().val()),r.refresh(),r.focus(),$(this).html(a.fm.i18n("TextArea")))})).prependTo(l.next())};return a.confObj.loader||(a.confObj.loader=$.Deferred(),n?(require.config({packages:[{name:"codemirror",location:i,main:"codemirror.min"}],map:{codemirror:{"codemirror/lib/codemirror":"codemirror"}}}),require(["codemirror","codemirror/addon/mode/loadmode.min","codemirror/mode/meta.min"],function(e){a.confObj.loader.resolve(e)})):a.fm.loadScript([i+"/codemirror.min.js"],function(){a.fm.loadScript([i+"/addon/mode/loadmode.min.js",i+"/mode/meta.min.js"],function(){a.confObj.loader.resolve(CodeMirror)})},{loadType:"tag"}),a.fm.loadCss(i+"/codemirror.css")),a.confObj.loader.done(r),o},close:function(e,t){t&&t.toTextArea()},save:function(e,t){t&&$(e).data("cm")&&(e.value=t.getValue())},focus:function(e,t){t&&$(e).data("cm")&&t.focus()},resize:function(e,t,i,n){t&&t.refresh()}},{setup:function(e,t){!t.UA.ltIE10&&t.options.cdns.simplemde||(this.disabled=!0)},info:{id:"simplemde",name:"SimpleMDE",iconImg:"img/editor-icons.png 0 -80"},exts:["md"],load:function(e){var t=this,i=this.fm,o=$(e).parent(),a=$.Deferred(),r=i.options.cdns.simplemde,s=function(i){var n,r,s,d=o.height(),c=o.outerHeight(!0)-d+14;e._setHeight=function(e){var t,i=e||o.height(),a=0;return o.children(".editor-toolbar,.editor-statusbar").each(function(){a+=$(this).outerHeight(!0)}),t=i-a-c,r.height(t),n.codemirror.refresh(),t},o.height(d),s={element:e,autofocus:!0},t.trigger("Prepare",{node:e,editorObj:i,instance:void 0,opts:s}),n=new i(s),a.resolve(n),r=$(n.codemirror.getWrapperElement()),r.css("min-height","50px").children(".CodeMirror-scroll").css("min-height","50px"),e._setHeight(d)};return t.confObj.loader||(t.confObj.loader=$.Deferred(),t.fm.loadCss(r+"/simplemde.min.css"),n?require([r+"/simplemde.min.js"],function(e){t.confObj.loader.resolve(e)}):t.fm.loadScript([r+"/simplemde.min.js"],function(){t.confObj.loader.resolve(SimpleMDE)},{loadType:"tag"})),t.confObj.loader.done(s),a},close:function(e,t){t&&t.toTextArea(),t=null},save:function(e,t){t&&(e.value=t.value())},focus:function(e,t){t&&t.codemirror.focus()},resize:function(e,t,i,n){t&&e._setHeight()}},{info:{id:"ckeditor",name:"CKEditor",iconImg:"img/editor-icons.png 0 0"},exts:["htm","html","xhtml"],setup:function(e,t){var i=this;t.options.cdns.ckeditor?(i.ckeOpts={},e.extraOptions&&(i.ckeOpts=Object.assign({},e.extraOptions.ckeditor||{}),e.extraOptions.managerUrl&&(i.managerUrl=e.extraOptions.managerUrl))):i.disabled=!0},load:function(e){var t=this,i=this.fm,n=$.Deferred(),o=function(){var o,a=$(e).parent(),r=a.closest(".elfinder-dialog"),s=a.height(),d=/([&?]getfile=)[^&]+/,c=t.confObj.managerUrl||window.location.href.replace(/#.*$/,""),l="ckeditor";d.test(c)?c=c.replace(d,"$1"+l):c+="?getfile="+l,a.height(s),o={startupFocus:!0,fullPage:!0,allowedContent:!0,filebrowserBrowseUrl:c,toolbarCanCollapse:!0,toolbarStartupExpanded:!i.UA.Mobile,removePlugins:"resize",extraPlugins:"colorbutton,justify,docprops",on:{instanceReady:function(o){var a=o.editor;a.resize("100%",s),r.one("beforedommove."+i.namespace,function(){a.destroy()}).one("dommove."+i.namespace,function(){t.load(e).done(function(e){t.instance=e})}),n.resolve(o.editor)}}},t.trigger("Prepare",{node:e,editorObj:CKEDITOR,instance:void 0,opts:o}),CKEDITOR.replace(e.id,Object.assign(o,t.confObj.ckeOpts)),CKEDITOR.on("dialogDefinition",function(e){var t=e.data.definition.dialog;t.on("show",function(e){i.getUI().append($(".cke_dialog_background_cover")).append(this.getElement().$)}),t.on("hide",function(e){$("body:first").append($(".cke_dialog_background_cover")).append(this.getElement().$)})})};return t.confObj.loader||(t.confObj.loader=$.Deferred(),window.CKEDITOR_BASEPATH=i.options.cdns.ckeditor+"/",$.getScript(i.options.cdns.ckeditor+"/ckeditor.js",function(){t.confObj.loader.resolve()})),t.confObj.loader.done(o),n},close:function(e,t){t&&t.destroy()},save:function(e,t){t&&(e.value=t.getData())},focus:function(e,t){t&&t.focus()},resize:function(e,t,i,n){t&&"ready"===t.status&&t.resize("100%",$(e).parent().height())}},{info:{id:"ckeditor5",name:"CKEditor5",iconImg:"img/editor-icons.png 0 -16"},exts:["htm","html","xhtml"],html:'<div class="edit-editor-ckeditor5"></div>',setup:function(e,t){var i=this;t.options.cdns.ckeditor5&&"function"==typeof window.Symbol&&"symbol"==typeof Symbol()?(i.ckeOpts={},e.extraOptions&&(e.extraOptions.ckeditor5Mode&&(i.ckeditor5Mode=e.extraOptions.ckeditor5Mode),i.ckeOpts=Object.assign({},e.extraOptions.ckeditor5||{}),i.ckeOpts.mode&&(i.ckeditor5Mode=i.ckeOpts.mode,delete i.ckeOpts.mode),e.extraOptions.managerUrl&&(i.managerUrl=e.extraOptions.managerUrl))):i.disabled=!0,t.bind("destroy",function(){i.editor=null})},prepare:function(e,t,i){$(e).height(e.editor.fm.getUI().height()-100)},init:function(e,t,i,n){var o=i.match(/^([\s\S]*<body[^>]*>)([\s\S]+)(<\/body>[\s\S]*)$/i),a="",r="",s="";this.css({width:"100%",height:"100%","box-sizing":"border-box"}),o?(a=o[1],r=o[2],s=o[3]):r=i,this.data("data",{header:a,body:r,footer:s}),this._setupSelEncoding(i)},load:function(e){var t,i=this,n=this.fm,o=$.Deferred(),a=i.confObj.ckeditor5Mode||"decoupled-document",r=function(){var e=n.lang.toLowerCase().replace("_","-");return"zh"===e.substr(0,2)&&"zh-cn"!==e&&(e="zh"),e}(),s=function(t){var s,c=$(e).parent();c.height(n.getUI().height()-100),s=Object.assign({toolbar:["heading","|","fontSize","fontFamily","|","bold","italic","underline","strikethrough","highlight","|","alignment","|","numberedList","bulletedList","blockQuote","indent","outdent","|","ckfinder","link","imageUpload","insertTable","mediaEmbed","|","undo","redo"],language:r},i.confObj.ckeOpts),i.trigger("Prepare",{node:e,editorObj:t,instance:void 0,opts:s}),t.create(e,s).then(function(t){var i,r,s=t.commands.get("ckfinder"),l=t.plugins.get("FileRepository"),p={};!t.ui.view.toolbar||"classic"!==a&&"decoupled-document"!==a||$(e).closest(".elfinder-dialog").children(".ui-widget-header").append($(t.ui.view.toolbar.element).css({marginRight:"-1em",marginLeft:"-1em"})),"classic"===a&&$(e).closest(".elfinder-edit-editor").css("overflow","auto"),s&&(i=function(e){return e&&e.mime.match(/^image\//i)},r=function(e){var i=t.commands.get("imageUpload");if(!i.isEnabled){var n=t.plugins.get("Notification"),o=t.locale.t;return void n.showWarning(o("Could not insert image at the current position."),{title:o("Inserting image failed"),namespace:"ckfinder"})}t.execute("imageInsert",{source:e})},s.execute=function(){var e=c.closest(".elfinder-dialog"),o=n.getCommand("getfile"),a=function(){p.hasVar&&(e.off("resize close",a),o.callback=p.callback,o.options.folders=p.folders,o.options.multiple=p.multi,n.commandMap.open=p.open,p.hasVar=!1)};e.trigger("togleminimize").one("resize close",a),p.callback=o.callback,p.folders=o.options.folders,p.multi=o.options.multiple,p.open=n.commandMap.open,p.hasVar=!0,o.callback=function(o){var a=[];return 1===o.length&&"directory"===o[0].mime?void n.one("open",function(){n.commandMap.open="getfile"}).getCommand("open").exec(o[0].hash):(n.getUI("cwd").trigger("unselectall"),$.each(o,function(e,o){i(o)?a.push(n.convAbsUrl(o.url)):t.execute("link",n.convAbsUrl(o.url))}),a.length&&r(a),void e.trigger("togleminimize"))},o.options.folders=!0,o.options.multiple=!0,n.commandMap.open="getfile",n.toast({mode:"info",msg:n.i18n("dblclickToSelect")})}),l.createUploadAdapter=function(e){return new d(e)},t.setData($(e).data("data").body),n.getUI().append($("body > div.ck-body")),$("div.ck-balloon-panel").css({"z-index":n.getMaximizeCss().zIndex+1}),o.resolve(t)})["catch"](function(e){n.error(e)})},d=function(e){var t=function(t,i,o){n.exec("upload",{files:[t]},void 0,n.cwd().hash).done(function(e){e.added&&e.added.length?n.url(e.added[0].hash,{async:!0}).done(function(e){i({"default":n.convAbsUrl(e)})}).fail(function(){o("errFileNotFound")}):o(n.i18n(e.error?e.error:"errUpload"))}).fail(function(e){var t=n.parseError(e);o(n.i18n(t?"userabort"===t?"errAbort":t:"errUploadNoFiles"))}).progress(function(t){e.uploadTotal=t.total,e.uploaded=t.progress})};this.upload=function(){return new Promise(function(i,n){e.file instanceof Promise||e.file&&"function"==typeof e.file.then?e.file.then(function(e){t(e,i,n)}):t(e.file,i,n)})},this.abort=function(){n.getUI().trigger("uploadabort")}};return i.confObj.editor?s(i.confObj.editor):(t=$.Deferred(),i.fm.loadScript([n.options.cdns.ckeditor5+"/"+a+"/ckeditor.js"],function(e){e||(e=window.BalloonEditor||window.InlineEditor||window.ClassicEditor||window.DecoupledEditor),"en"!==n.lang?i.fm.loadScript([n.options.cdns.ckeditor5+"/"+a+"/translations/"+r+".js"],function(i){t.resolve(e)},{tryRequire:!0,loadType:"tag",error:function(i){r="en",t.resolve(e)}}):t.resolve(e)},{tryRequire:!0,loadType:"tag"}),t.done(function(e){i.confObj.editor=e,s(e)})),o},getContent:function(){var e=$(this).data("data");return e.header+e.body+e.footer},close:function(e,t){t&&t.destroy()},save:function(e,t){var i=$(e),n=i.data("data");t&&(n.body=t.getData(),i.data("data",n))},focus:function(e,t){$(e).trigger("focus")}},{info:{id:"tinymce",name:"TinyMCE",iconImg:"img/editor-icons.png 0 -64"},exts:["htm","html","xhtml"],setup:function(e,t){var i=this;t.options.cdns.tinymce?(i.mceOpts={},e.extraOptions?(i.uploadOpts=Object.assign({},e.extraOptions.uploadOpts||{}),i.mceOpts=Object.assign({},e.extraOptions.tinymce||{})):i.uploadOpts={}):i.disabled=!0},load:function(e){var t=this,i=this.fm,n=$.Deferred(),o=function(){var o,a,r,s=$(e).show().parent(),d=s.closest(".elfinder-dialog"),c=s.height(),l=s.outerHeight(!0)-c,p=function(){var e;tinymce.activeEditor.windowManager.windows?(e=tinymce.activeEditor.windowManager.windows[0],a=$(e?e.getEl():void 0).hide(),r=$("#mce-modal-block").hide()):a=$(".tox-dialog-wrap").hide()},m=function(){r&&r.show(),a&&a.show()},f=tinymce.majorVersion;s.height(c),e._setHeight=function(e){if(f<5){var t,i=$(this).parent(),n=e||i.innerHeight(),o=0;i.find(".mce-container-body:first").children(".mce-top-part,.mce-statusbar").each(function(){o+=$(this).outerHeight(!0)}),t=n-o-l,i.find(".mce-edit-area iframe:first").height(t)}},o={selector:"#"+e.id,resize:!1,plugins:"preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help",toolbar:"formatselect | bold italic strikethrough forecolor backcolor | link image media | alignleft aligncenter alignright alignjustify | numlist bullist outdent indent | removeformat",image_advtab:!0,init_instance_callback:function(o){e._setHeight(c),d.one("beforedommove."+i.namespace,function(){tinymce.execCommand("mceRemoveEditor",!1,e.id)}).one("dommove."+i.namespace,function(){t.load(e).done(function(e){t.instance=e})}),n.resolve(o)},file_picker_callback:function(e,t,n){var o=i.getCommand("getfile"),a=function(){r.hasVar&&(o.callback=r.callback,o.options.folders=r.folders,o.options.multiple=r.multi,i.commandMap.open=r.open,r.hasVar=!1),d.off("resize close",a),m()},r={};return r.callback=o.callback,r.folders=o.options.folders,r.multi=o.options.multiple,r.open=i.commandMap.open,r.hasVar=!0,o.callback=function(t){var o,a;return"directory"===t.mime?void i.one("open",function(){i.commandMap.open="getfile"}).getCommand("open").exec(t.hash):(o=i.convAbsUrl(t.url),a=t.name+" ("+i.formatSize(t.size)+")","file"==n.filetype&&e(o,{text:a,title:a}),"image"==n.filetype&&e(o,{alt:a}),"media"==n.filetype&&e(o),void d.trigger("togleminimize"))},o.options.folders=!0,o.options.multiple=!1,i.commandMap.open="getfile",p(),d.trigger("togleminimize").one("resize close",a),i.toast({mode:"info",msg:i.i18n("dblclickToSelect")}),!1},images_upload_handler:function(e,n,o){var a=e.blob(),r=function(e){var t=e.data.dialog||{};(t.hasClass("elfinder-dialog-error")||t.hasClass("elfinder-confirm-upload"))&&(p(),t.trigger("togleminimize").one("resize close",s),i.unbind("dialogopened",r));
},s=function(){d.off("resize close",s),m()},c=!0;a.name&&(c=void 0),i.bind("dialogopened",r).exec("upload",Object.assign({files:[a],clipdata:c},t.confObj.uploadOpts),void 0,i.cwd().hash).done(function(e){e.added&&e.added.length?i.url(e.added[0].hash,{async:!0}).done(function(e){m(),n(i.convAbsUrl(e))}).fail(function(){o(i.i18n("errFileNotFound"))}):o(i.i18n(e.error?e.error:"errUpload"))}).fail(function(e){var t=i.parseError(e);t&&("errUnknownCmd"===t?t="errPerm":"userabort"===t&&(t="errAbort")),o(i.i18n(t?t:"errUploadNoFiles"))})}},f>=5&&(o.height="100%"),t.trigger("Prepare",{node:e,editorObj:tinymce,instance:void 0,opts:o}),tinymce.init(Object.assign(o,t.confObj.mceOpts))};return t.confObj.loader||(t.confObj.loader=$.Deferred(),t.fm.loadScript([i.options.cdns.tinymce+(i.options.cdns.tinymce.match(/\.js/)?"":"/tinymce.min.js")],function(){t.confObj.loader.resolve()},{loadType:"tag"})),t.confObj.loader.done(o),n},close:function(e,t){t&&tinymce.execCommand("mceRemoveEditor",!1,e.id)},save:function(e,t){t&&t.save()},focus:function(e,t){t&&t.focus()},resize:function(e,t,i,n){t&&e._setHeight()}},{info:{id:"zohoeditor",name:"Zoho Editor",iconImg:"img/editor-icons.png 0 -32",cmdCheck:"ZohoOffice",preventGet:!0,hideButtons:!0,syncInterval:15e3,canMakeEmpty:!0,integrate:{title:"Zoho Office API",link:"https://www.zoho.com/officeapi/"}},mimes:["application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.oasis.opendocument.text","application/rtf","text/html","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.oasis.opendocument.spreadsheet","application/vnd.sun.xml.calc","text/csv","text/tab-separated-values","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/vnd.openxmlformats-officedocument.presentationml.slideshow","application/vnd.oasis.opendocument.presentation","application/vnd.sun.xml.impress"],html:'<iframe style="width:100%;max-height:100%;border:none;"></iframe>',setup:function(e,t){(t.UA.Mobile||t.UA.ltIE8)&&(this.disabled=!0)},prepare:function(e,t,i){var n=e.editor.fm.getUI();$(e).height(n.height()),t.width=Math.max(t.width||0,.8*n.width())},init:function(e,t,i,n){var o=this,a=$(this).hide(),r=n.getUI("toast"),s=$('<div class="elfinder-edit-spinner elfinder-edit-zohoeditor"></div>').html('<span class="elfinder-spinner-text">'+n.i18n("nowLoading")+'</span><span class="elfinder-spinner"></span>').appendTo(a.parent()),d=function(){var e="";return $.each(n.customData,function(t,i){e+="&"+encodeURIComponent(t)+"="+encodeURIComponent(i)}),e};$(o).data("xhr",n.request({data:{cmd:"editor",name:o.editor.confObj.info.cmdCheck,method:"init","args[target]":t.hash,"args[lang]":n.lang,"args[cdata]":d()},preventDefault:!0}).done(function(e){var t;e.zohourl?(t={css:{height:"100%"}},o.editor.trigger("Prepare",{node:o,editorObj:void 0,instance:a,opts:t}),a.attr("src",e.zohourl).show().css(t.css),e.warning&&(r.appendTo(o.closest(".ui-dialog")),n.toast({msg:n.i18n(e.warning),mode:"warning",timeOut:0,onHidden:function(){1===r.children().length&&r.appendTo(n.getUI())},button:{text:"btnYes"}}))):(e.error&&n.error(e.error),o.elfinderdialog("destroy"))}).fail(function(e){e&&n.error(e),o.elfinderdialog("destroy")}).always(function(){s.remove()}))},load:function(){},getContent:function(){},save:function(){},beforeclose:c,close:function(e){var t=(this.fm,$(e).data("xhr"));"pending"===t.state()&&t.reject()}},{info:{id:"ziparchive",name:"btnMount",iconImg:"img/toolbar.png 0 -416",cmdCheck:"ZipArchive",edit:function(e,t){var i=this,n=$.Deferred();return i.request({data:{cmd:"netmount",protocol:"ziparchive",host:e.hash,path:e.phash},preventFail:!0,notify:{type:"netmount",cnt:1,hideCnt:!0}}).done(function(e){var t;e.added&&e.added.length&&(e.added[0].phash&&(t=i.file(e.added[0].phash))&&(t.dirs||(t.dirs=1,i.change({changed:[t]}))),i.one("netmountdone",function(){i.exec("open",e.added[0].hash),i.one("opendone",function(){e.toast&&i.toast(e.toast)})})),n.resolve()}).fail(function(e){n.reject(e)}),n}},mimes:["application/zip"],load:function(){},save:function(){}},{info:{id:"textarea",name:"TextArea",useTextAreaEvent:!0},load:function(e){this.trigger("Prepare",{node:e,editorObj:void 0,instance:void 0,opts:{}}),e.setSelectionRange&&e.setSelectionRange(0,0),$(e).trigger("focus").show()},save:function(){}},{info:{id:"onlineconvert",name:"Online Convert",iconImg:"img/editor-icons.png 0 -144",cmdCheck:"OnlineConvert",preventGet:!0,hideButtons:!0,single:!0,converter:!0,canMakeEmpty:!1,integrate:{title:"ONLINE-CONVERT.COM",link:"https://online-convert.com"}},mimes:["*"],html:'<div style="width:100%;max-height:100%;"></div>',setup:function(e,t){var i=e.extraOptions.onlineConvert||{maxSize:100,showLink:!0};i.maxSize&&(this.info.maxSize=1048576*i.maxSize),this.set=Object.assign({url:"https://%s.online-convert.com%s?external_url=",conv:{Archive:{"7Z":{},BZ2:{ext:"bz"},GZ:{},ZIP:{}},Audio:{MP3:{},OGG:{ext:"oga"},WAV:{},WMA:{},AAC:{},AIFF:{ext:"aif"},FLAC:{},M4A:{},MMF:{},OPUS:{ext:"oga"}},Document:{DOC:{},DOCX:{},HTML:{},ODT:{},PDF:{},PPT:{},PPTX:{},RTF:{},SWF:{},TXT:{}},eBook:{AZW3:{ext:"azw"},ePub:{},FB2:{ext:"xml"},LIT:{},LRF:{},MOBI:{},PDB:{},PDF:{},"PDF-eBook":{ext:"pdf"},TCR:{}},Hash:{Adler32:{},"Apache-htpasswd":{},Blowfish:{},CRC32:{},CRC32B:{},Gost:{},Haval128:{},MD4:{},MD5:{},RIPEMD128:{},RIPEMD160:{},SHA1:{},SHA256:{},SHA384:{},SHA512:{},Snefru:{},"Std-DES":{},Tiger128:{},"Tiger128-calculator":{},"Tiger128-converter":{},Tiger160:{},Tiger192:{},Whirlpool:{}},Image:{BMP:{},EPS:{ext:"ai"},GIF:{},EXR:{},ICO:{},JPG:{},PNG:{},SVG:{},TGA:{},TIFF:{ext:"tif"},WBMP:{},WebP:{}},Video:{"3G2":{},"3GP":{},AVI:{},FLV:{},HLS:{ext:"m3u8"},MKV:{},MOV:{},MP4:{},"MPEG-1":{ext:"mpeg"},"MPEG-2":{ext:"mpeg"},OGG:{ext:"ogv"},OGV:{},WebM:{},WMV:{},Android:{link:"/convert-video-for-%s",ext:"mp4"},Blackberry:{link:"/convert-video-for-%s",ext:"mp4"},DPG:{link:"/convert-video-for-%s",ext:"avi"},iPad:{link:"/convert-video-for-%s",ext:"mp4"},iPhone:{link:"/convert-video-for-%s",ext:"mp4"},iPod:{link:"/convert-video-for-%s",ext:"mp4"},"Nintendo-3DS":{link:"/convert-video-for-%s",ext:"avi"},"Nintendo-DS":{link:"/convert-video-for-%s",ext:"avi"},PS3:{link:"/convert-video-for-%s",ext:"mp4"},Wii:{link:"/convert-video-for-%s",ext:"avi"},Xbox:{link:"/convert-video-for-%s",ext:"wmv"}}},catExts:{Hash:"txt"},link:'<div class="elfinder-edit-onlineconvert-link"><a href="https://www.online-convert.com" target="_blank"><span class="elfinder-button-icon"></span>ONLINE-CONVERT.COM</a></div>',useTabs:!(!$.fn.tabs||t.UA.iOS)},i)},prepare:function(e,t,i){var n=e.editor.fm.getUI();$(e).height(n.height()),t.width=Math.max(t.width||0,.8*n.width())},init:function(e,t,i,n){var a,r,s=this,d=s.editor.confObj,c=d.set,l=n.getUI("toast"),p={},m=n.uploadMimeCheck("application/zip",t.phash),f=$("base").length?document.location.href.replace(/#.*$/,""):"",u=function(e,t){var i;return c.catExts[e]?c.catExts[e]:c.conv[e]&&(i=c.conv[e][t])?(i.ext||t).toLowerCase():t.toLowerCase()},g=function(e,t){var i,o,a;o="undefined"==typeof d.api?n.request({data:{cmd:"editor",name:"OnlineConvert",method:"init"},preventDefault:!0}):$.Deferred().resolve({api:d.api}),e=e.toLowerCase(),o.done(function(n){d.api=n.api,d.api&&(e?i="?category="+e:(i="",e="all"),d.conversions||(d.conversions={}),a=d.conversions[e]?$.Deferred().resolve(d.conversions[e]):$.getJSON("https://api2.online-convert.com/conversions"+i),a.done(function(i){d.conversions[e]=i,$.each(i,function(e,t){h[c.useTabs?"children":"find"](".onlineconvert-category-"+t.category).children(".onlineconvert-"+t.target).trigger("makeoption",t)}),t&&t()}))})},h=function(){var e=$("<div></div>").on("click","button",function(){var e=$(this),t=e.data("opts")||null,i=e.closest(".onlineconvert-category").data("cname"),n=e.data("conv");d.api===!0&&k({category:i,convert:n,options:t})}).on("change",function(e){var t=$(e.target),i=t.parent(),o=t.closest(".elfinder-edit-onlineconvert-button").children("button:first"),a=o.data("opts")||{},r="boolean"===i.data("type")?t.is(":checked"):t.val();if(e.stopPropagation(),r&&("integer"===i.data("type")&&(r=parseInt(r)),i.data("pattern"))){var s=new RegExp(i.data("pattern"));s.test(r)||(requestAnimationFrame(function(){n.error('"'+n.escape(r)+'" is not match to "/'+n.escape(i.data("pattern"))+'/"')}),r=null)}r?a[t.parent().data("optkey")]=r:delete a[i.data("optkey")],o.data("opts",a)}),i=$("<ul></ul>"),a=function(e,t){var i,o,a,r=$("<p></p>").data("optkey",e).data("type",t.type),s="",d="",c=!1;return t.description&&r.attr("title",n.i18n(t.description)),t.pattern&&r.data("pattern",t.pattern),r.append($("<span></span>").text(n.i18n(e)+" : ")),"boolean"===t.type?((t["default"]||(c="allow_multiple_outputs"===e&&!m))&&(s=" checked",c&&(d=" disabled"),o=this.children("button:first"),i=o.data("opts")||{},i[e]=!0,o.data("opts",i)),r.append($('<input type="checkbox" value="true"'+s+d+"/>"))):t["enum"]?(a=$("<select></select>").append($('<option value=""></option>').text("Select...")),$.each(t["enum"],function(e,t){a.append($('<option value="'+t+'"></option>').text(t))}),r.append(a)):r.append($('<input type="text" value=""/>')),r},r=function(e){var t=this,i=$('<span class="elfinder-button-icon elfinder-button-icon-preference"></span>').on("click",function(){n.toggle()}),n=$('<div class="elfinder-edit-onlinconvert-options"></div>').hide();e.options&&$.each(e.options,function(e,i){"download_password"!==e&&n.append(a.call(t,e,i))}),t.append(i,n)},s=+new Date,l=0;return d.ext2mime||(d.ext2mime=Object.assign(n.arrayFlip(n.mimeTypes),o)),$.each(c.conv,function(o,a){var c=o.toLowerCase(),m="elfinder-edit-onlineconvert-"+c+s,g=$('<div id="'+m+'" class="onlineconvert-category onlineconvert-category-'+c+'"></div>').data("cname",o);$.each(a,function(e,i){var a=e.toLowerCase(),s=u(o,e);d.ext2mime[s]||("audio"===c||"image"===c||"video"===c?d.ext2mime[s]=c+"/x-"+a:d.ext2mime[s]="application/octet-stream"),n.uploadMimeCheck(d.ext2mime[s],t.phash)&&g.append($('<div class="elfinder-edit-onlineconvert-button onlineconvert-'+a+'"></div>').on("makeoption",function(e,t){var i=$(this);i.children(".elfinder-button-icon-preference").length||r.call(i,t)}).append($("<button></button>").text(e).data("conv",e)))}),g.children().length&&(i.append($("<li></li>").append($("<a></a>").attr("href",f+"#"+m).text(o))),e.append(g),p[c]=l++)}),c.useTabs?e.prepend(i).tabs({beforeActivate:function(e,t){g(t.newPanel.data("cname"))}}):$.each(c.conv,function(t){var i=t.toLowerCase();e.append($('<fieldset class="onlineconvert-fieldset-'+i+'"></fieldset>').append($("<legend></legend>").text(t)).append(e.children(".onlineconvert-category-"+i)))}),e}(),v=$(this).append(h,c.showLink?$(c.link):null),b=$('<div class="elfinder-edit-spinner elfinder-edit-onlineconvert"></div>').hide().html('<span class="elfinder-spinner-text">'+n.i18n("nowLoading")+'</span><span class="elfinder-spinner"></span>').appendTo(v.parent()),x=$('<div class="elfinder-quicklook-info-progress"></div>').appendTo(b),y=null,w=function(){return y?$.Deferred().resolve(y):(b.show(),n.forExternalUrl(t.hash,{progressBar:x}).done(function(e){y=e}).fail(function(e){e&&n.error(e),s.elfinderdialog("destroy")}).always(function(){b.hide()}))},k=function(e){$(s).data("dfrd",w().done(function(i){v.fadeOut(),j({info:"Start conversion request."}),n.request({data:{cmd:"editor",name:"OnlineConvert",method:"api","args[category]":e.category.toLowerCase(),"args[convert]":e.convert.toLowerCase(),"args[options]":JSON.stringify(e.options),"args[source]":n.convAbsUrl(i),"args[filename]":n.splitFileExtention(t.name)[0]+"."+u(e.category,e.convert),"args[mime]":t.mime},preventDefault:!0}).done(function(t){O(t.apires,e.category,e.convert)}).fail(function(e){e&&n.error(e),s.elfinderdialog("destroy")})}))},O=function(e,t,i){var o,a=[];e&&e.id?(o=e.status,"failed"===o.code?(b.hide(),e.errors&&e.errors.length&&$.each(e.errors,function(e,t){t.message&&a.push(t.message)}),n.error(a.length?a:o.info),v.fadeIn()):"completed"===o.code?M(e):(j(o),setTimeout(function(){C(e.id)},1e3))):(l.appendTo(s.closest(".ui-dialog")),e.message&&n.toast({msg:n.i18n(e.message),mode:"error",timeOut:5e3,onHidden:function(){1===l.children().length&&l.appendTo(n.getUI())}}),n.toast({msg:n.i18n("editorConvNoApi"),mode:"error",timeOut:3e3,onHidden:function(){1===l.children().length&&l.appendTo(n.getUI())}}),b.hide(),v.show())},j=function(e){b.show().children(".elfinder-spinner-text").text(e.info)},C=function(e){n.request({data:{cmd:"editor",name:"OnlineConvert",method:"api","args[jobid]":e},preventDefault:!0}).done(function(e){O(e.apires)}).fail(function(e){e&&n.error(e),s.elfinderdialog("destroy")})},M=function(e){var i=e.output,o=(e.id,"");b.hide(),i&&i.length&&(s.elfinderdialog("destroy"),$.each(i,function(e,t){t.uri&&(o+=t.uri+"\n")}),n.upload({target:t.phash,files:[o],type:"text",extraData:{contentSaveId:"OnlineConvert-"+e.id}}))},T="document";v.parent().css({overflow:"auto"}).addClass("overflow-scrolling-touch"),(r=t.mime.match(/^(audio|image|video)/))&&(T=r[1]),c.useTabs?p[T]&&h.tabs("option","active",p[T]):(a=Object.keys(c.conv).length,$.each(c.conv,function(e){return e.toLowerCase()===T?(g(e,function(){$.each(c.conv,function(e){e.toLowerCase()!==T&&g(e)})}),!1):void a--}),a||$.each(c.conv,function(e){g(e)}),v.parent().scrollTop(h.children(".onlineconvert-fieldset-"+T).offset().top))},load:function(){},getContent:function(){},save:function(){},close:function(e){var t=(this.fm,$(e).data("dfrd"));t&&"pending"===t.state()&&t.reject()}}]},window.elFinder);