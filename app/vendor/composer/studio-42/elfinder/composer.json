{"name": "studio-42/elfinder", "description": "File manager for web", "license": "BSD-3-<PERSON><PERSON>", "homepage": "http://elfinder.org", "authors": [{"name": "<PERSON>", "homepage": "http://std42.ru", "email": "<EMAIL>"}, {"name": "Troex Nevelin", "homepage": "http://std42.ru", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://xoops.hypweb.net", "email": "<EMAIL>"}, {"name": "Community contributions", "homepage": "https://github.com/Studio-42/elFinder/contributors"}], "require": {"php": ">=5.2"}, "suggest": {"kunalvarma05/dropbox-php-sdk": "VolumeDriver `Dropbox`2 require `kunalvarma05/dropbox-php-sdk.", "google/apiclient": "VolumeDriver GoogleDrive require `google/apiclient:^2.0.", "barryvdh/elfinder-flysystem-driver": "VolumeDriver for elFinder to use Flysystem as a root.", "nao-pon/flysystem-google-drive": "require in GoogleDrive network volume mounting with Flysystem."}, "autoload": {"classmap": ["php"]}}