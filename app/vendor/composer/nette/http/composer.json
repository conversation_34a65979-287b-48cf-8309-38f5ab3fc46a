{"name": "nette/http", "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "keywords": ["nette", "http", "request", "response", "session", "security", "url", "proxy", "cookies"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": "8.1 - 8.4", "nette/utils": "^4.0.4"}, "require-dev": {"nette/di": "^3.0", "nette/tester": "^2.4", "nette/security": "^3.0", "tracy/tracy": "^2.8", "phpstan/phpstan": "^1.0"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "suggest": {"ext-fileinfo": "to detect MIME type of uploaded files by Nette\\Http\\FileUpload", "ext-gd": "to use image function in Nette\\Http\\FileUpload", "ext-session": "to use Nette\\Http\\Session", "ext-intl": "to support punycode by Nette\\Http\\Url"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "scripts": {"phpstan": "phpstan analyse", "tester": "tester tests -s"}, "extra": {"branch-alias": {"dev-master": "3.3-dev"}}}