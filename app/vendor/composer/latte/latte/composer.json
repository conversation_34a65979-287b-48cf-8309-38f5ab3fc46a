{"name": "latte/latte", "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "keywords": ["template", "nette", "security", "engine", "html", "twig", "context-sensitive", "escaping"], "homepage": "https://latte.nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": "8.0 - 8.4", "ext-json": "*", "ext-tokenizer": "*"}, "require-dev": {"nette/tester": "^2.5", "tracy/tracy": "^2.10", "nette/utils": "^4.0", "phpstan/phpstan": "^1", "nette/php-generator": "^4.0"}, "suggest": {"ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "ext-fileinfo": "to use filter |datastream", "ext-intl": "to use Latte\\Engine::setLocale()", "nette/utils": "to use filter |webalize", "nette/php-generator": "to use tag {templatePrint}"}, "conflict": {"nette/application": "<3.1.7", "nette/caching": "<3.1.4"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "bin": ["bin/latte-lint"], "scripts": {"phpstan": "phpstan analyse", "tester": "tester tests -s"}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}