<?php

/**
 * This file is part of the Latte (https://latte.nette.org)
 * Copyright (c) 2008 <PERSON> (https://davidgrudl.com)
 */

declare(strict_types=1);

namespace Latte\Compiler;

use Latte\Compiler\Nodes\Php as Node;
use Latte\Compiler\Nodes\Php\Expression;
use Latte\Compiler\Nodes\Php\Scalar;


/** @internal generated trait used by TagParser */
abstract class TagParserData
{
	/** Symbol number of error recovery token */
	protected const ErrorSymbol = 1;

	/** Action number signifying default action */
	protected const DefaultAction = -8190;

	/** Rule number signifying that an unexpected token was encountered */
	protected const UnexpectedTokenRule = 8191;

	protected const Yy2Tblstate = 256;

	/** Number of non-leaf states */
	protected const NumNonLeafStates = 354;

	/** Map of lexer tokens to internal symbols */
	protected const TokenToSymbol = [
		0,     113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   48,    108,   113,   109,   47,    113,   113,
		102,   103,   45,    43,    2,     44,    39,    46,    113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   22,    106,
		35,    7,     37,    21,    59,    113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   61,    113,   107,   27,    113,   113,   100,   113,   113,
		113,   98,    101,   113,   113,   113,   113,   113,   113,   99,    113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   104,   26,    105,   50,    113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
		113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,   1,     3,     4,     5,
		6,     8,     9,     10,    11,    12,    13,    14,    15,    16,    17,    18,    19,    20,    23,    24,    25,    28,    29,    30,
		31,    32,    33,    34,    36,    38,    40,    41,    42,    49,    51,    52,    53,    54,    55,    56,    57,    58,    60,    62,
		63,    64,    65,    66,    67,    68,    69,    70,    71,    72,    73,    74,    75,    76,    77,    78,    79,    80,    81,    82,
		83,    84,    85,    86,    87,    88,    89,    90,    110,   91,    92,    93,    94,    111,   112,   95,    96,    97,
	];

	/** Map of states to a displacement into the self::Action table. The corresponding action for this
	 *  state/symbol pair is self::Action[self::ActionBase[$state] + $symbol]. If self::ActionBase[$state] is 0, the
	 *  action is defaulted, i.e. self::ActionDefault[$state] should be used instead. */
	protected const ActionBase = [
		297,   326,   326,   326,   326,   99,    121,   326,   273,   177,   230,   326,   406,   406,   406,   406,   219,   219,   219,   219,
		295,   295,   267,   262,   401,   402,   403,   404,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,
		-43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,
		-43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,
		-43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,
		-43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   133,   165,   185,   416,   432,   427,   436,   460,   462,   459,   463,
		468,   52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    126,   182,   528,   213,   213,
		213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   213,   549,   549,   549,   437,
		362,   204,   412,   38,    110,   110,   513,   513,   513,   513,   513,   447,   81,    81,    81,    81,    500,   500,   271,   271,
		271,   271,   271,   271,   271,   271,   271,   272,   135,   135,   454,   13,    275,   275,   275,   228,   228,   228,   228,   228,
		181,   108,   108,   108,   236,   355,   409,   276,   207,   207,   207,   207,   207,   207,   285,   469,   -21,   132,   132,   205,
		167,   167,   132,   378,   -13,   122,   -34,   208,   332,   221,   143,   158,   2,     405,   245,   252,   287,   142,   219,   269,
		269,   219,   219,   219,   411,   64,    64,    64,    147,   180,   39,    278,   445,   278,   278,   278,   42,    -74,   268,   352,
		344,   351,   268,   36,    76,    62,    354,   357,   352,   352,   98,    62,    62,    62,    283,   299,   277,   109,   65,    277,
		288,   288,   104,   28,    363,   360,   369,   342,   341,   408,   197,   234,   281,   280,   337,   198,   363,   360,   369,   259,
		6,     310,   282,   282,   282,   282,   282,   282,   282,   282,   282,   282,   197,   452,   33,    291,   374,   375,   17,    385,
		395,   286,   284,   453,   282,   263,   306,   260,   464,   331,   24,    197,   466,   270,   279,   274,   292,   384,   289,   467,
		397,   305,   398,   455,   130,   376,   134,   457,   140,   451,   253,   400,   450,   458,   0,     -43,   -43,   -43,   -43,   -43,
		-43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,   -43,
		-43,   -43,   0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,
		52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    52,    0,     447,   52,    52,
		52,    52,    52,    52,    52,    0,     108,   108,   108,   108,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,
		-22,   108,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   -22,   0,     0,     0,     0,     0,     0,
		0,     378,   288,   288,   288,   288,   288,   288,   378,   378,   0,     0,     0,     0,     108,   108,   0,     0,     378,   288,
		0,     0,     0,     0,     0,     0,     0,     219,   219,   219,   378,   0,     0,     288,   288,   0,     0,     0,     268,   0,
		0,     0,     0,     0,     0,     282,   33,    282,   282,   282,
	];

	/** Table of actions. Indexed according to self::ActionBase comment. */
	protected const Action = [
		34,    35,    -269,  32,    -269,  36,    -50,   37,    178,   179,   38,    39,    40,    41,    42,    43,    44,    -48,   1,     192,
		45,    557,   558,   204,   -47,   539,   381,   -222,  0,     555,   284,   522,   241,   242,   195,   7,     285,   286,   11,    -47,
		-220,  287,   288,   207,   537,   177,   539,   -222,  -222,  -222,  541,   540,   563,   561,   562,   54,    55,    56,    -8190, 29,
		-220,  -220,  -220,  416,   15,    223,   289,   21,    196,   -220,  197,   541,   540,   23,    566,   57,    58,    59,    101,   60,
		61,    62,    63,    64,    65,    66,    67,    68,    69,    70,    71,    72,    73,    74,    75,    76,    77,    78,    79,    80,
		190,   194,   362,   363,   361,   -269,  233,   381,   418,   -269,  417,   383,   81,    -8190, -8190, -8190, -8191, -8191, -8191, -8191,
		72,    73,    74,    75,    362,   363,   361,   360,   359,   -8190, 379,   -8190, 380,   -8190, -8190, -8190, 105,   -8190, -8190, -8190,
		106,   372,   98,    -78,   12,    -78,   107,   289,   366,   360,   359,   76,    77,    78,    79,    80,    195,   194,   -267,  171,
		-267,  193,   46,    372,   13,    200,   338,   295,   81,    -78,   366,   17,    296,   368,   234,   235,   367,   373,   297,   298,
		362,   363,   361,   193,   46,    -8190, -8190, 200,   641,   295,   24,    19,    642,   -217,  296,   368,   234,   235,   367,   373,
		297,   298,   427,   -22,   95,    360,   359,   -8190, -8190, -8190, -8190, -8190, 25,    -217,  -217,  -217,  -8190, -8190, -8190, 372,
		-23,   -265,  -217,  -265,  427,   -8190, 366,   -8190, -8190, -8190, 418,   -8190, 417,   362,   363,   361,   206,   -184,  -52,   193,
		46,    96,    -223,  200,   14,    295,   -78,   360,   359,   -184,  296,   368,   234,   235,   367,   373,   297,   298,   360,   359,
		108,   -267,  -223,  -223,  -223,  -267,  31,    -17,   26,    30,    -183,  -183,  372,   379,   -16,   380,   362,   363,   361,   366,
		427,   18,    -183,  -183,  97,    104,   211,   212,   213,   208,   209,   210,   193,   46,    171,   240,   200,   -223,  295,   191,
		198,   360,   359,   296,   368,   234,   235,   367,   373,   297,   298,   99,    73,    74,    75,    372,   289,   -223,  -223,  -223,
		199,   27,    366,   418,   -265,  417,   -183,  194,   -265,  362,   363,   361,   1,     427,   321,   193,   46,    249,   -183,  200,
		381,   295,   81,    555,   381,   102,   296,   368,   234,   235,   367,   373,   297,   298,   360,   359,   288,   381,   -8190, 643,
		-8190, 345,   -252,  567,   100,   -8190, -8190, -8190, 372,   277,   -192,  163,   568,   49,    20,    366,   427,   50,    289,   223,
		289,   239,   274,   -8190, 289,   -8190, -8190, -8190, 193,   46,    391,   638,   200,   225,   295,   33,    258,   3,     176,   296,
		368,   234,   235,   367,   373,   297,   298,   -251,  -250,  362,   363,   361,   -226,  245,   246,   247,   -219,  47,    48,    16,
		82,    83,    84,    85,    86,    87,    88,    89,    90,    91,    92,    93,    94,    -225,  360,   359,   -219,  -219,  -219,  -224,
		-8190, -8190, -8190, 2,     4,     -219,  5,     381,   372,   630,   362,   363,   361,   6,     8,     366,   9,     -226,  -8190, 10,
		-8190, -8190, 28,    468,   470,   51,    22,    182,   193,   46,    -218,  52,    200,   -217,  295,   195,   188,   189,   238,   296,
		368,   234,   235,   367,   373,   297,   298,   276,   510,   372,   -218,  -218,  -218,  -217,  -217,  -217,  366,   100,   553,   -218,
		428,   446,   -217,  525,   531,   533,   535,   585,   -29,   365,   364,   -225,  53,    376,   505,   377,   -8190, -8190, -8190, 329,
		369,   368,   371,   370,   367,   373,   374,   375,   360,   359,   333,   -8190, -8190, -8190, -8190, 544,   -8190, -8190, -8190, 511,
		-8190, -8190, -8190, -8191, -8191, -8191, -8191, -8191, 610,   418,   103,   417,   -8190, -8190, -8190, 257,   353,   -29,   640,   552,
		392,   1,     639,   521,   341,   637,   249,   594,   608,   381,   -8190, 582,   555,   598,   633,   347,   0,     556,   0,     0,
		0,     0,     0,     0,     287,   288,   0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     49,    0,     0,     0,     0,     0,     223,   289,
	];

	/** Table indexed analogously to self::Action. If self::ActionCheck[self::ActionBase[$state] + $symbol] != $symbol
	 *  then the action is defaulted, i.e. self::ActionDefault[$state] should be used instead. */
	protected const ActionCheck = [
		43,    44,    0,     77,    2,     48,    0,     50,    51,    52,    53,    54,    55,    56,    57,    58,    59,    0,     61,    62,
		63,    64,    65,    66,    0,     68,    69,    61,    0,     72,    73,    105,   75,    76,    21,    2,     79,    80,    2,     0,
		61,    84,    85,    86,    66,    6,     68,    81,    82,    83,    93,    94,    95,    96,    97,    3,     4,     5,     71,    102,
		81,    82,    83,    85,    2,     108,   109,   2,     26,    90,    28,    93,    94,    21,    87,    23,    24,    25,    2,     27,
		28,    29,    30,    31,    32,    33,    34,    35,    36,    37,    38,    39,    40,    41,    42,    43,    44,    45,    46,    47,
		2,     49,    3,     4,     5,     103,   2,     69,    95,    107,   97,    2,     60,    3,     4,     5,     35,    36,    37,    38,
		39,    40,    41,    42,    3,     4,     5,     28,    29,    3,     66,    21,    68,    23,    24,    25,    6,     27,    28,    29,
		6,     42,    104,   0,     2,     2,     6,     109,   49,    28,    29,    43,    44,    45,    46,    47,    21,    49,    0,     26,
		2,     62,    63,    42,    22,    66,    44,    68,    60,    26,    49,    6,     73,    74,    75,    76,    77,    78,    79,    80,
		3,     4,     5,     62,    63,    3,     4,     66,    66,    68,    91,    6,     70,    61,    73,    74,    75,    76,    77,    78,
		79,    80,    103,   22,    7,     28,    29,    3,     4,     5,     43,    44,    91,    81,    82,    83,    3,     4,     5,     42,
		22,    0,     90,    2,     103,   21,    49,    23,    24,    25,    95,    27,    97,    3,     4,     5,     103,   90,    103,   62,
		63,    7,     61,    66,    102,   68,    103,   28,    29,    102,   73,    74,    75,    76,    77,    78,    79,    80,    28,    29,
		7,     103,   81,    82,    83,    107,   61,    22,    91,    61,    90,    90,    42,    66,    22,    68,    3,     4,     5,     49,
		103,   22,    102,   102,   22,    22,    81,    82,    83,    81,    82,    83,    62,    63,    26,    90,    66,    61,    68,    22,
		26,    28,    29,    73,    74,    75,    76,    77,    78,    79,    80,    104,   40,    41,    42,    42,    109,   81,    82,    83,
		28,    91,    49,    95,    103,   97,    90,    49,    107,   3,     4,     5,     61,    103,   67,    62,    63,    66,    102,   66,
		69,    68,    60,    72,    69,    61,    73,    74,    75,    76,    77,    78,    79,    80,    28,    29,    85,    69,    71,    70,
		85,    78,    102,   87,    91,    3,     4,     5,     42,    74,    90,    90,    87,    102,   104,   49,    103,   102,   109,   108,
		109,   90,    103,   21,    109,   23,    24,    25,    62,    63,    91,    105,   66,    61,    68,    98,    99,    100,   101,   73,
		74,    75,    76,    77,    78,    79,    80,    102,   102,   3,     4,     5,     102,   81,    82,    83,    61,    91,    92,    7,
		8,     9,     10,    11,    12,    13,    14,    15,    16,    17,    18,    19,    20,    102,   28,    29,    81,    82,    83,    102,
		3,     4,     5,     102,   102,   90,    102,   69,    42,    71,    3,     4,     5,     102,   102,   49,    102,   102,   21,    102,
		23,    24,    102,   51,    52,    102,   88,    89,    62,    63,    61,    102,   66,    61,    68,    21,    102,   102,   102,   73,
		74,    75,    76,    77,    78,    79,    80,    103,   103,   42,    81,    82,    83,    81,    82,    83,    49,    91,    103,   90,
		103,   103,   90,    103,   103,   103,   103,   103,   103,   62,    63,    102,   104,   66,    103,   68,    3,     4,     5,     103,
		73,    74,    75,    76,    77,    78,    79,    80,    28,    29,    103,   3,     4,     5,     21,    103,   23,    24,    25,    103,
		27,    28,    29,    30,    31,    32,    33,    34,    103,   95,    22,    97,    3,     4,     5,     104,   106,   103,   105,   107,
		105,   61,    105,   105,   105,   105,   66,    105,   105,   69,    21,    107,   72,    107,   107,   107,   -1,    108,   -1,    -1,
		-1,    -1,    -1,    -1,    84,    85,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		-1,    -1,    102,   -1,    -1,    -1,    -1,    -1,    108,   109,
	];

	/** Map of states to their default action */
	protected const ActionDefault = [
		8191,  263,   263,   31,    263,   8191,  8191,  263,   8191,  8191,  8191,  29,    8191,  8191,  8191,  29,    8191,  8191,  8191,  8191,
		39,    29,    8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  215,   215,   215,   8191,  8191,  8191,  8191,  8191,  8191,  8191,
		8191,  8191,  8191,  8191,  8191,  8191,  10,    8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,
		8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,
		8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,
		8191,  29,    8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  264,   264,   8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,
		8191,  1,     270,   271,   83,    77,    216,   266,   268,   79,    82,    80,    43,    44,    56,    120,   122,   154,   121,   96,
		101,   102,   103,   104,   105,   106,   107,   108,   109,   110,   111,   112,   113,   94,    95,    166,   155,   153,   152,   118,
		119,   125,   93,    8191,  123,   124,   142,   143,   140,   141,   144,   8191,  145,   146,   147,   148,   8191,  8191,  8191,  8191,
		8191,  8191,  8191,  8191,  8191,  8191,  8191,  126,   69,    69,    69,    8191,  8191,  11,    8191,  8191,  8191,  8191,  8191,  8191,
		206,   132,   133,   135,   206,   205,   150,   8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  211,   115,   117,   189,
		127,   128,   97,    8191,  8191,  8191,  210,   8191,  278,   217,   217,   217,   217,   34,    34,    34,    8191,  89,    34,    8191,
		8191,  34,    34,    34,    8191,  8191,  8191,  8191,  195,   223,   217,   138,   8191,  129,   130,   131,   57,    8191,  8191,  193,
		182,   8191,  2,     28,    28,    28,    8191,  236,   237,   238,   28,    28,    28,    28,    170,   36,    71,    28,    28,    71,
		8191,  8191,  28,    8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  200,   8191,  221,   234,   185,   15,    20,    21,    8191,
		200,   219,   136,   137,   139,   158,   159,   160,   161,   162,   163,   164,   261,   8191,  257,   188,   8191,  8191,  217,   8191,
		8191,  277,   8191,  217,   134,   8191,  196,   241,   8191,  218,   217,   262,   8191,  8191,  8191,  59,    60,    8191,  8191,  8191,
		8191,  197,   8191,  8191,  8191,  8191,  8191,  8191,  8191,  8191,  55,    8191,  8191,  8191,
	];

	/** Map of non-terminals to a displacement into the self::Goto table. The corresponding goto state for this
	 *  non-terminal/state pair is self::Goto[self::GotoBase[$nonTerminal] + $state] (unless defaulted) */
	protected const GotoBase = [
		0,     0,     -1,    0,     0,     0,     107,   0,     254,   -126,  16,    -28,   0,     235,   -230,  0,     0,     0,     0,     166,
		200,   52,    -13,   262,   -2,    58,    0,     63,    0,     57,    75,    0,     0,     -61,   3,     -34,   -5,    124,   0,     0,
		41,    0,     0,     65,    0,     0,     154,   0,     0,     0,     24,    0,     0,     0,     0,     -77,   -44,   0,     0,     14,
		25,    12,    36,    37,    -67,   0,     0,     -51,   66,    0,     -15,   109,   115,   44,    0,     0,
	];

	/** Table of states to goto after reduction. Indexed according to self::GotoBase comment. */
	protected const Goto = [
		111,   111,   110,   111,   316,   317,   111,   271,   272,   273,   110,   316,   317,   275,   320,   139,   127,   128,   127,   124,
		124,   116,   137,   129,   129,   129,   129,   124,   109,   126,   126,   126,   121,   302,   303,   251,   304,   305,   306,   307,
		308,   309,   310,   311,   454,   454,   122,   123,   112,   113,   114,   115,   117,   135,   136,   138,   156,   159,   160,   161,
		164,   165,   166,   167,   168,   169,   170,   172,   173,   174,   175,   187,   201,   202,   203,   220,   221,   253,   254,   255,
		324,   140,   141,   142,   143,   144,   145,   146,   147,   148,   149,   150,   151,   152,   153,   154,   157,   118,   119,   129,
		130,   120,   158,   131,   132,   155,   133,   134,   180,   180,   180,   180,   327,   252,   180,   315,   315,   315,   180,   634,
		635,   636,   612,   183,   185,   186,   185,   415,   415,   415,   415,   536,   536,   536,   415,   415,   415,   415,   415,   430,
		224,   597,   597,   597,   538,   538,   538,   538,   538,   538,   538,   538,   538,   538,   538,   538,   236,   609,   609,   609,
		609,   609,   609,   250,   330,   217,   218,   229,   318,   323,   222,   230,   231,   232,   300,   300,   554,   554,   554,   554,
		554,   554,   554,   554,   554,   332,   352,   396,   595,   595,   576,   576,   576,   576,   576,   576,   576,   576,   576,   576,
		576,   574,   574,   574,   574,   574,   574,   574,   574,   574,   574,   574,   301,   301,   301,   301,   301,   301,   301,   301,
		301,   301,   301,   228,   602,   603,   530,   532,   319,   400,   228,   228,   534,   584,   586,   587,   435,   340,   386,   228,
		228,   644,   405,   445,   444,   342,   387,   337,   426,   348,   394,   628,   228,   408,   526,   299,   299,   299,   299,   503,
		215,   299,   527,   312,   312,   299,   312,   628,   629,   312,   412,   419,   421,   420,   422,   268,   269,   599,   600,   601,
		629,   331,   349,   181,   181,   439,   0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     350,   529,   0,     440,
		0,     0,     0,     441,   0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		389,   389,   389,   0,     0,     389,   0,     0,     389,   389,   389,   0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     326,   0,     0,
		0,     0,     0,     0,     0,     237,   0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,     403,   403,
	];

	/** Table indexed analogously to self::Goto. If self::GotoCheck[self::GotoBase[$nonTerminal] + $state] != $nonTerminal
	 *  then the goto state is defaulted, i.e. self::GotoDefault[$nonTerminal] should be used. */
	protected const GotoCheck = [
		2,     2,     2,     2,     14,    14,    2,     36,    36,    36,    2,     14,    14,    14,    61,    2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     6,     6,     6,     6,     67,    71,    6,     55,    55,    55,    6,     9,
		9,     9,     72,    6,     6,     6,     6,     33,    33,    33,    33,    33,    33,    33,    33,    33,    33,    33,    33,    37,
		64,    67,    67,    67,    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,    64,    67,    67,    67,
		67,    67,    67,    22,    22,    22,    22,    22,    22,    22,    22,    22,    22,    22,    24,    24,    24,    24,    24,    24,
		24,    24,    24,    24,    24,    46,    46,    19,    67,    67,    59,    59,    59,    59,    59,    59,    59,    59,    59,    59,
		59,    60,    60,    60,    60,    60,    60,    60,    60,    60,    60,    60,    62,    62,    62,    62,    62,    62,    62,    62,
		62,    62,    62,    10,    70,    70,    35,    35,    20,    21,    10,    10,    35,    35,    35,    35,    11,    11,    13,    10,
		10,    10,    11,    11,    11,    11,    13,    25,    29,    11,    11,    73,    10,    27,    11,    8,     8,     8,     8,     43,
		63,    8,     50,    23,    23,    8,     23,    73,    73,    23,    30,    30,    30,    30,    30,    68,    68,    68,    68,    68,
		73,    23,    34,    6,     6,     40,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    10,    10,    -1,    40,
		-1,    -1,    -1,    40,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		-1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		6,     6,     6,     -1,    -1,    6,     -1,    -1,    6,     6,     6,     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		-1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		-1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
		-1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    8,     -1,    -1,
		-1,    -1,    -1,    -1,    -1,    8,     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    23,    23,
	];

	/** Map of non-terminals to the default state to goto after their reduction */
	protected const GotoDefault = [
		-8192, 283,   125,   262,   357,   358,   184,   378,   325,   607,   593,   384,   263,   614,   281,   280,   453,   343,   278,   395,
		344,   399,   162,   290,   291,   334,   270,   407,   243,   424,   256,   335,   336,   260,   346,   548,   265,   429,   264,   244,
		438,   442,   452,   259,   519,   279,   328,   523,   351,   282,   528,   583,   261,   292,   266,   545,   248,   219,   293,   226,
		216,   313,   205,   214,   627,   227,   294,   581,   267,   589,   596,   314,   613,   626,   322,   339,
	];

	/** Map of rules to the non-terminal on their left-hand side, i.e. the non-terminal to use for
	 *  determining the state to goto after reduction. */
	protected const RuleToNonTerminal = [
		0,     1,     1,     1,     1,     6,     6,     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,
		7,     7,     8,     8,     8,     9,     9,     10,    11,    11,    4,     4,     12,    12,    14,    14,    15,    15,    16,    17,
		17,    18,    18,    19,    19,    5,     5,     21,    21,    21,    21,    25,    25,    26,    26,    27,    27,    29,    29,    29,
		29,    30,    30,    30,    30,    31,    31,    32,    32,    28,    28,    34,    34,    35,    35,    36,    36,    37,    37,    37,
		37,    20,    38,    38,    39,    39,    3,     3,     40,    41,    41,    41,    41,    2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
		2,     2,     2,     2,     2,     2,     2,     2,     2,     42,    45,    45,    48,    49,    49,    50,    51,    51,    51,    51,
		51,    51,    55,    33,    33,    56,    56,    56,    43,    43,    43,    53,    53,    47,    47,    59,    60,    60,    24,    62,
		62,    62,    62,    44,    44,    44,    44,    44,    44,    44,    44,    44,    44,    44,    44,    46,    46,    58,    58,    58,
		58,    65,    65,    65,    52,    52,    52,    66,    66,    66,    66,    66,    66,    66,    22,    22,    22,    22,    22,    67,
		67,    70,    69,    57,    57,    57,    57,    57,    57,    57,    54,    54,    54,    68,    68,    68,    23,    61,    71,    71,
		72,    72,    72,    72,    13,    13,    13,    13,    13,    13,    13,    13,    63,    63,    63,    63,    64,    74,    73,    73,
		73,    73,    73,    73,    73,    73,    73,    75,    75,    75,    75,
	];

	/** Map of rules to the length of their right-hand side, which is the number of elements that have to
	 *  be popped from the stack(s) on reduction. */
	protected const RuleToLength = [
		1,     2,     2,     2,     2,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
		1,     1,     1,     1,     1,     1,     1,     1,     0,     1,     2,     0,     1,     3,     0,     1,     0,     1,     7,     0,
		2,     1,     3,     3,     4,     1,     3,     1,     2,     1,     1,     2,     0,     1,     3,     4,     6,     1,     2,     1,
		1,     1,     1,     1,     1,     3,     3,     3,     3,     0,     1,     0,     2,     2,     4,     1,     3,     1,     2,     2,
		3,     2,     3,     1,     2,     2,     1,     2,     3,     0,     3,     3,     4,     1,     3,     3,     3,     4,     1,     1,
		2,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     2,     2,     2,     2,     3,     3,
		3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     2,     2,     2,     2,
		3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,     5,     4,     3,     3,     4,     4,     2,     2,
		2,     2,     2,     2,     2,     1,     8,     12,    9,     3,     0,     4,     2,     1,     3,     2,     2,     4,     2,     4,
		4,     6,     1,     1,     1,     1,     1,     1,     1,     1,     3,     1,     1,     0,     1,     1,     3,     5,     3,     4,
		1,     1,     3,     1,     1,     1,     1,     1,     1,     1,     1,     1,     3,     2,     3,     0,     1,     1,     3,     1,
		1,     1,     1,     1,     1,     3,     1,     1,     4,     1,     4,     6,     4,     4,     1,     1,     3,     3,     3,     1,
		4,     1,     3,     1,     4,     3,     3,     3,     3,     3,     1,     3,     1,     1,     3,     1,     4,     1,     3,     1,
		1,     1,     3,     0,     1,     2,     3,     4,     3,     4,     2,     2,     2,     2,     1,     2,     1,     1,     1,     4,
		3,     3,     3,     3,     3,     6,     3,     1,     1,     2,     1,
	];

	/** Map of symbols to their names */
	protected const SymbolToName = [
		'end',
		'error',
		"','",
		"'or'",
		"'xor'",
		"'and'",
		"'=>'",
		"'='",
		"'+='",
		"'-='",
		"'*='",
		"'/='",
		"'.='",
		"'%='",
		"'&='",
		"'|='",
		"'^='",
		"'<<='",
		"'>>='",
		"'**='",
		"'??='",
		"'?'",
		"':'",
		"'??'",
		"'||'",
		"'&&'",
		"'|'",
		"'^'",
		"'&'",
		"'&'",
		"'=='",
		"'!='",
		"'==='",
		"'!=='",
		"'<=>'",
		"'<'",
		"'<='",
		"'>'",
		"'>='",
		"'.'",
		"'<<'",
		"'>>'",
		"'in'",
		"'+'",
		"'-'",
		"'*'",
		"'/'",
		"'%'",
		"'!'",
		"'instanceof'",
		"'~'",
		"'++'",
		"'--'",
		"'(int)'",
		"'(float'",
		"'(string)'",
		"'(array)'",
		"'(object)'",
		"'(bool)'",
		"'@'",
		"'**'",
		"'['",
		"'new'",
		"'clone'",
		'integer',
		'floating-point number',
		'identifier',
		'variable name',
		'constant',
		'variable',
		'number',
		'string content',
		'quoted string',
		"'match'",
		"'default'",
		"'function'",
		"'fn'",
		"'return'",
		"'use'",
		"'isset'",
		"'empty'",
		"'->'",
		"'?->'",
		"'??->'",
		"'list'",
		"'array'",
		"'heredoc start'",
		"'heredoc end'",
		"'\${'",
		"'{\$'",
		"'::'",
		"'...'",
		"'(expand)'",
		'fully qualified name',
		'namespaced name',
		"'null'",
		"'true'",
		"'false'",
		"'e'",
		"'m'",
		"'a'",
		"'f'",
		"'('",
		"')'",
		"'{'",
		"'}'",
		"';'",
		"']'",
		"'\"'",
		"'$'",
		"'\\\\'",
		'whitespace',
		'comment',
	];

	/** Temporary value containing the result of last semantic action (reduction) */
	protected mixed $semValue = null;

	/** Semantic value stack (contains values of tokens and semantic action results) */
	protected array $semStack;

	/** @var Token[] Start attribute stack */
	protected array $startTokenStack;


	protected function reduce(int $rule, int $pos): void
	{
		(match ($rule) {
			0, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 28, 29, 57, 70, 72, 93, 98, 99, 165, 182, 184, 188, 189, 191, 192, 194, 205, 210, 211, 216, 217, 219, 220, 221, 222, 224, 226, 227, 229, 234, 235, 239, 243, 250, 252, 253, 255, 260, 278, 290 => fn() => $this->semValue = $this->semStack[$pos],
			2 => fn() => $this->semValue = new Node\ModifierNode($this->semStack[$pos], position: $this->startTokenStack[$pos]->position),
			3 => fn() => $this->semValue = new Expression\ArrayNode($this->semStack[$pos], position: $this->startTokenStack[$pos]->position),
			22, 23, 24, 25, 26, 62, 63, 64 => fn() => $this->semValue = new Node\IdentifierNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			27 => fn() => $this->semValue = new Expression\VariableNode(substr($this->semStack[$pos], 1), $this->startTokenStack[$pos]->position),
			30, 40, 51, 81, 90, 91, 150, 151, 171, 172, 190, 218, 225, 251, 254, 286 => fn() => $this->semValue = $this->semStack[$pos - 1],
			31, 39, 52, 73, 89, 170, 193 => fn() => $this->semValue = [],
			32, 41, 53, 75, 83, 86, 173, 259, 274 => fn() => $this->semValue = [$this->semStack[$pos]],
			33, 42, 54, 66, 68, 76, 82, 174, 258 => function () use ($pos) {
				$this->semStack[$pos - 2][] = $this->semStack[$pos];
				$this->semValue = $this->semStack[$pos - 2];
			},
			34, 36 => fn() => $this->semValue = false,
			35, 37 => fn() => $this->semValue = true,
			38 => fn() => $this->semValue = new Expression\MatchNode($this->semStack[$pos - 4], $this->semStack[$pos - 1], $this->startTokenStack[$pos - 6]->position),
			43 => fn() => $this->semValue = new Node\MatchArmNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			44 => fn() => $this->semValue = new Node\MatchArmNode(null, $this->semStack[$pos], $this->startTokenStack[$pos - 3]->position),
			45 => fn() => $this->semValue = [null, ...$this->semStack[$pos]],
			46 => fn() => $this->semValue = [$this->semStack[$pos - 2], ...$this->semStack[$pos]],
			47, 49 => fn() => $this->semValue = [$this->semStack[$pos], false],
			48 => fn() => $this->semValue = [$this->semStack[$pos], true],
			50 => fn() => $this->semValue = [$this->convertArrayToList($this->semStack[$pos]), false],
			55 => fn() => $this->semValue = new Node\ParameterNode($this->semStack[$pos], null, $this->semStack[$pos - 3], $this->semStack[$pos - 2], $this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			56 => fn() => $this->semValue = new Node\ParameterNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->semStack[$pos - 5], $this->semStack[$pos - 4], $this->semStack[$pos - 3], $this->startTokenStack[$pos - 5]->position),
			58 => fn() => $this->semValue = new Node\NullableTypeNode($this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			59 => fn() => $this->semValue = new Node\UnionTypeNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			60 => fn() => $this->semValue = new Node\IntersectionTypeNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			61 => fn() => $this->semValue = TagParser::handleBuiltinTypes($this->semStack[$pos]),
			65, 67 => fn() => $this->semValue = [$this->semStack[$pos - 2], $this->semStack[$pos]],
			69, 71, 215 => fn() => $this->semValue = null,
			74, 92 => fn() => $this->semValue = $this->semStack[$pos - 2],
			77 => fn() => $this->semValue = new Node\ArgumentNode($this->semStack[$pos], false, false, null, $this->startTokenStack[$pos]->position),
			78 => fn() => $this->semValue = new Node\ArgumentNode($this->semStack[$pos], true, false, null, $this->startTokenStack[$pos - 1]->position),
			79 => fn() => $this->semValue = new Node\ArgumentNode($this->semStack[$pos], false, true, null, $this->startTokenStack[$pos - 1]->position),
			80 => fn() => $this->semValue = new Node\ArgumentNode($this->semStack[$pos], false, false, $this->semStack[$pos - 2], $this->startTokenStack[$pos - 2]->position),
			84, 85 => fn() => $this->semValue = new Expression\FilterCallNode($this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			87, 272, 273 => function () use ($pos) {
				$this->semStack[$pos - 1][] = $this->semStack[$pos];
				$this->semValue = $this->semStack[$pos - 1];
			},
			88 => fn() => $this->semValue = new Node\FilterNode($this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			94, 96 => fn() => $this->semValue = new Expression\AssignNode($this->semStack[$pos - 2], $this->semStack[$pos], false, $this->startTokenStack[$pos - 2]->position),
			95 => fn() => $this->semValue = new Expression\AssignNode($this->convertArrayToList($this->semStack[$pos - 2]), $this->semStack[$pos], false, $this->startTokenStack[$pos - 2]->position),
			97 => fn() => $this->semValue = new Expression\AssignNode($this->semStack[$pos - 3], $this->semStack[$pos], true, $this->startTokenStack[$pos - 3]->position),
			100 => fn() => $this->semValue = new Expression\CloneNode($this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			101 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '+', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			102 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '-', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			103 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '*', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			104 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '/', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			105 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '.', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			106 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '%', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			107 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '&', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			108 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '|', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			109 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '^', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			110 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '<<', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			111 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '>>', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			112 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '**', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			113 => fn() => $this->semValue = new Expression\AssignOpNode($this->semStack[$pos - 2], '??', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			114 => fn() => $this->semValue = new Expression\PostOpNode($this->semStack[$pos - 1], '++', $this->startTokenStack[$pos - 1]->position),
			115 => fn() => $this->semValue = new Expression\PreOpNode($this->semStack[$pos], '++', $this->startTokenStack[$pos - 1]->position),
			116 => fn() => $this->semValue = new Expression\PostOpNode($this->semStack[$pos - 1], '--', $this->startTokenStack[$pos - 1]->position),
			117 => fn() => $this->semValue = new Expression\PreOpNode($this->semStack[$pos], '--', $this->startTokenStack[$pos - 1]->position),
			118 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '||', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			119 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '&&', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			120 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], 'or', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			121 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], 'and', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			122 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], 'xor', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			123, 124 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '&', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			125 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '^', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			126 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '.', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			127 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '+', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			128 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '-', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			129 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '*', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			130 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '/', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			131 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '%', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			132 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '<<', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			133 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '>>', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			134 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '**', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			135 => fn() => $this->semValue = new Expression\InNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			136 => fn() => $this->semValue = new Expression\UnaryOpNode($this->semStack[$pos], '+', $this->startTokenStack[$pos - 1]->position),
			137 => fn() => $this->semValue = new Expression\UnaryOpNode($this->semStack[$pos], '-', $this->startTokenStack[$pos - 1]->position),
			138 => fn() => $this->semValue = new Expression\NotNode($this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			139 => fn() => $this->semValue = new Expression\UnaryOpNode($this->semStack[$pos], '~', $this->startTokenStack[$pos - 1]->position),
			140 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '===', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			141 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '!==', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			142 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '==', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			143 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '!=', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			144 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '<=>', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			145 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '<', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			146 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '<=', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			147 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '>', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			148 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '>=', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			149 => fn() => $this->semValue = new Expression\InstanceofNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			152 => fn() => $this->semValue = new Expression\TernaryNode($this->semStack[$pos - 4], $this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 4]->position),
			153 => fn() => $this->semValue = new Expression\TernaryNode($this->semStack[$pos - 3], null, $this->semStack[$pos], $this->startTokenStack[$pos - 3]->position),
			154 => fn() => $this->semValue = new Expression\TernaryNode($this->semStack[$pos - 2], $this->semStack[$pos], null, $this->startTokenStack[$pos - 2]->position),
			155 => fn() => $this->semValue = new Expression\BinaryOpNode($this->semStack[$pos - 2], '??', $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			156 => fn() => $this->semValue = new Expression\IssetNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			157 => fn() => $this->semValue = new Expression\EmptyNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			158 => fn() => $this->semValue = new Expression\CastNode('int', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			159 => fn() => $this->semValue = new Expression\CastNode('float', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			160 => fn() => $this->semValue = new Expression\CastNode('string', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			161 => fn() => $this->semValue = new Expression\CastNode('array', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			162 => fn() => $this->semValue = new Expression\CastNode('object', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			163 => fn() => $this->semValue = new Expression\CastNode('bool', $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			164 => fn() => $this->semValue = new Expression\ErrorSuppressNode($this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
			166 => fn() => $this->semValue = new Expression\ClosureNode((bool) $this->semStack[$pos - 6], $this->semStack[$pos - 4], [], $this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 7]->position),
			167 => fn() => $this->semValue = new Expression\ClosureNode((bool) $this->semStack[$pos - 10], $this->semStack[$pos - 8], $this->semStack[$pos - 6], $this->semStack[$pos - 5], $this->semStack[$pos - 2], $this->startTokenStack[$pos - 11]->position),
			168 => fn() => $this->semValue = new Expression\ClosureNode((bool) $this->semStack[$pos - 7], $this->semStack[$pos - 5], $this->semStack[$pos - 3], $this->semStack[$pos - 2], null, $this->startTokenStack[$pos - 8]->position),
			169 => fn() => $this->semValue = new Expression\NewNode($this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			175 => fn() => $this->semValue = new Node\ClosureUseNode($this->semStack[$pos], $this->semStack[$pos - 1], $this->startTokenStack[$pos - 1]->position),
			176, 178 => fn() => $this->semValue = $this->checkFunctionName(new Expression\FunctionCallNode($this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position)),
			177, 179 => fn() => $this->semValue = $this->checkFunctionName(new Expression\FunctionCallableNode($this->semStack[$pos - 3], $this->startTokenStack[$pos - 3]->position)),
			180 => fn() => $this->semValue = new Expression\StaticMethodCallNode($this->semStack[$pos - 3], $this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 3]->position),
			181 => fn() => $this->semValue = new Expression\StaticMethodCallableNode($this->semStack[$pos - 5], $this->semStack[$pos - 3], $this->startTokenStack[$pos - 5]->position),
			183, 185, 186 => fn() => $this->semValue = new Node\NameNode($this->semStack[$pos], Node\NameNode::KindNormal, $this->startTokenStack[$pos]->position),
			187 => fn() => $this->semValue = new Node\NameNode($this->semStack[$pos], Node\NameNode::KindFullyQualified, $this->startTokenStack[$pos]->position),
			195 => fn() => $this->semValue = new Expression\ConstantFetchNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			196 => fn() => $this->semValue = new Expression\ClassConstantFetchNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			197 => fn() => $this->semValue = new Expression\ClassConstantFetchNode($this->semStack[$pos - 4], $this->semStack[$pos - 1], $this->startTokenStack[$pos - 4]->position),
			198 => fn() => $this->semValue = new Expression\ArrayNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 2]->position),
			199 => fn() => $this->semValue = new Expression\ArrayNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			200 => function () use ($pos) {
				$this->semValue = $this->semStack[$pos];
				$this->shortArrays->attach($this->semValue);
			},
			201 => fn() => $this->semValue = Scalar\StringNode::parse($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			202 => fn() => $this->semValue = Scalar\InterpolatedStringNode::parse($this->semStack[$pos - 1], $this->startTokenStack[$pos - 2]->position),
			203 => fn() => $this->semValue = Scalar\IntegerNode::parse($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			204 => fn() => $this->semValue = Scalar\FloatNode::parse($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			206, 287 => fn() => $this->semValue = new Scalar\StringNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			207 => fn() => $this->semValue = new Scalar\BooleanNode(true, $this->startTokenStack[$pos]->position),
			208 => fn() => $this->semValue = new Scalar\BooleanNode(false, $this->startTokenStack[$pos]->position),
			209 => fn() => $this->semValue = new Scalar\NullNode($this->startTokenStack[$pos]->position),
			212 => fn() => $this->semValue = $this->parseDocString($this->semStack[$pos - 2], [$this->semStack[$pos - 1]], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position, $this->startTokenStack[$pos]->position),
			213 => fn() => $this->semValue = $this->parseDocString($this->semStack[$pos - 1], [], $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position, $this->startTokenStack[$pos]->position),
			214 => fn() => $this->semValue = $this->parseDocString($this->semStack[$pos - 2], $this->semStack[$pos - 1], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position, $this->startTokenStack[$pos]->position),
			223 => fn() => $this->semValue = new Expression\ConstantFetchNode(new Node\NameNode($this->semStack[$pos], Node\NameNode::KindNormal, $this->startTokenStack[$pos]->position), $this->startTokenStack[$pos]->position),
			228, 244, 279 => fn() => $this->semValue = new Expression\ArrayAccessNode($this->semStack[$pos - 3], $this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			230 => fn() => $this->semValue = new Expression\MethodCallNode($this->semStack[$pos - 3], $this->semStack[$pos - 1], $this->semStack[$pos], false, $this->startTokenStack[$pos - 3]->position),
			231 => fn() => $this->semValue = new Expression\MethodCallableNode($this->semStack[$pos - 5], $this->semStack[$pos - 3], $this->startTokenStack[$pos - 5]->position),
			232 => fn() => $this->semValue = new Expression\MethodCallNode($this->semStack[$pos - 3], $this->semStack[$pos - 1], $this->semStack[$pos], true, $this->startTokenStack[$pos - 3]->position),
			233 => fn() => $this->semValue = new Expression\MethodCallNode(new Expression\BinaryOpNode($this->semStack[$pos - 3], '??', new Scalar\NullNode($this->startTokenStack[$pos - 3]->position), $this->startTokenStack[$pos - 3]->position), $this->semStack[$pos - 1], $this->semStack[$pos], true, $this->startTokenStack[$pos - 3]->position),
			236, 245, 280 => fn() => $this->semValue = new Expression\PropertyFetchNode($this->semStack[$pos - 2], $this->semStack[$pos], false, $this->startTokenStack[$pos - 2]->position),
			237, 246, 281 => fn() => $this->semValue = new Expression\PropertyFetchNode($this->semStack[$pos - 2], $this->semStack[$pos], true, $this->startTokenStack[$pos - 2]->position),
			238, 247, 282 => fn() => $this->semValue = new Expression\PropertyFetchNode(new Expression\BinaryOpNode($this->semStack[$pos - 2], '??', new Scalar\NullNode($this->startTokenStack[$pos - 2]->position), $this->startTokenStack[$pos - 2]->position), $this->semStack[$pos], true, $this->startTokenStack[$pos - 2]->position),
			240 => fn() => $this->semValue = new Expression\VariableNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position),
			241 => function () use ($pos) {
				$var = $this->semStack[$pos]->name;
				$this->semValue = \is_string($var)
					? new Node\VarLikeIdentifierNode($var, $this->startTokenStack[$pos]->position)
					: $var;
			},
			242, 248, 249 => fn() => $this->semValue = new Expression\StaticPropertyFetchNode($this->semStack[$pos - 2], $this->semStack[$pos], $this->startTokenStack[$pos - 2]->position),
			256 => fn() => $this->semValue = $this->convertArrayToList(new Expression\ArrayNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 3]->position)),
			257 => function () use ($pos) {
				$this->semValue = $this->semStack[$pos];
				$end = count($this->semValue) - 1;
				if (
					$this->semValue[$end]->value instanceof Expression\TemporaryNode
					&& !$this->semValue[$end]->value->value
				) {
					array_pop($this->semValue);
				}
			},
			261 => fn() => $this->semValue = new Node\ArrayItemNode(new Expression\TemporaryNode($this->semStack[$pos], $this->startTokenStack[$pos]->position), null, false, false, $this->startTokenStack[$pos]->position),
			262 => fn() => $this->semValue = new Node\ArrayItemNode(new Expression\TemporaryNode($this->semStack[$pos], $this->startTokenStack[$pos - 2]->position), $this->semStack[$pos - 2], false, false, $this->startTokenStack[$pos - 2]->position),
			263 => fn() => $this->semValue = new Node\ArrayItemNode(new Expression\TemporaryNode(null), null, false, false, $this->startTokenStack[$pos]->position),
			264 => fn() => $this->semValue = new Node\ArrayItemNode($this->semStack[$pos], null, false, false, $this->startTokenStack[$pos]->position),
			265 => fn() => $this->semValue = new Node\ArrayItemNode($this->semStack[$pos], null, true, false, $this->startTokenStack[$pos - 1]->position),
			266, 268 => fn() => $this->semValue = new Node\ArrayItemNode($this->semStack[$pos], $this->semStack[$pos - 2], false, false, $this->startTokenStack[$pos - 2]->position),
			267, 269 => fn() => $this->semValue = new Node\ArrayItemNode($this->semStack[$pos], $this->semStack[$pos - 3], true, false, $this->startTokenStack[$pos - 3]->position),
			270, 271 => fn() => $this->semValue = new Node\ArrayItemNode($this->semStack[$pos], null, false, true, $this->startTokenStack[$pos - 1]->position),
			275 => fn() => $this->semValue = [$this->semStack[$pos - 1], $this->semStack[$pos]],
			276 => fn() => $this->semValue = new Node\InterpolatedStringPartNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			277 => fn() => $this->semValue = new Expression\VariableNode($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			283, 284 => fn() => $this->semValue = new Expression\VariableNode($this->semStack[$pos - 1], $this->startTokenStack[$pos - 2]->position),
			285 => fn() => $this->semValue = new Expression\ArrayAccessNode($this->semStack[$pos - 4], $this->semStack[$pos - 2], $this->startTokenStack[$pos - 5]->position),
			288 => fn() => $this->semValue = TagParser::parseOffset($this->semStack[$pos], $this->startTokenStack[$pos]->position),
			289 => fn() => $this->semValue = TagParser::parseOffset('-' . $this->semStack[$pos], $this->startTokenStack[$pos - 1]->position),
		})();
	}
}
