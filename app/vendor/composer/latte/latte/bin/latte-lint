#!/usr/bin/env php
<?php

declare(strict_types=1);

if (
	!(is_file($file = __DIR__ . '/../vendor/autoload.php') && include $file) &&
	!(is_file($file = __DIR__ . '/../../../autoload.php') && include $file)
) {
	fwrite(STDERR, "Install packages using Composer.\n");
	exit(1);
}


echo '
Latte linter
------------
';

if ($argc < 2) {
	echo "Usage: latte-lint <path>\n";
	exit(1);
}

if ($debug = in_array('--debug', $argv, true)) {
	echo "Debug mode\n";
}
if ($strict = in_array('--strict', $argv, true)) {
	echo "Strict mode\n";
}

$path = $argv[1];
$linter = new Latte\Tools\Linter(debug: $debug, strict: $strict);
$ok = $linter->scanDirectory($path);
exit($ok ? 0 : 1);
