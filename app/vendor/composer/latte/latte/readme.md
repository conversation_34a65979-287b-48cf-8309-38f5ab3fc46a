[![Latte](https://github.com/nette/latte/assets/194960/76c098ff-427c-4b30-9676-97e2d8fd4a56)](https://latte.nette.org)

 <!---->

<h3>

✅ The [only truly secure](https://latte.nette.org/en/safety-first) templating system for PHP<br>
✅ [You already know the syntax](https://latte.nette.org/en/syntax)<br>
✅ Highly mature, fast, and widely used library

</h3>

 <!---->

Latte is the safest templating system for PHP. It is cleverly designed and easy to learn for those familiar with PHP, as they can quickly adopt its basic tags.
A wide range of useful features will significantly simplify your work. It provides top-notch protection against critical vulnerabilities and allows you to focus on creating high-quality applications without worrying about their security.

🟨 Only 1% of programmers [can pass this quiz](https://blog.nette.org/en/quiz-can-you-defend-against-xss-vulnerability)!

 <!---->

Getting started
=======

<h3>

1️⃣ First, familiarize yourself with [Latte syntax](https://latte.nette.org/en/syntax) and [try it online](https://fiddle.nette.org/latte/#9cc0cf6d89)<br>
2️⃣ Take a look at the basic set of [tags](https://latte.nette.org/en/tags) and [filters](https://latte.nette.org/en/filters)<br>
3️⃣ Render a template with a [few lines of PHP code](https://latte.nette.org/en/develop)

</h3>

 <!---->

Do you like Latte? Are you looking forward to the new features?

[![Buy me a coffee](https://files.nette.org/icons/donation-3.svg)](https://github.com/sponsors/dg)

Thank you!
