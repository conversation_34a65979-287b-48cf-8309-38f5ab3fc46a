<?php

declare(strict_types=1);

namespace PHPSTORM_META;

expectedArguments(\<PERSON>\Debugger::log(), 1, \<PERSON>\ILogger::DEBUG, \Tracy\ILogger::INFO, \Tracy\ILogger::WARNING, \Tracy\ILogger::ERROR, \<PERSON>\ILogger::EXCE<PERSON><PERSON><PERSON>, \<PERSON>\ILogger::CRITICAL);
expectedArguments(\Tracy\ILogger::log(), 1, \Tracy\ILogger::DEBUG, \Tracy\ILogger::INFO, \Tracy\ILogger::WARNING, \Tracy\ILogger::ERROR, \Tracy\ILogger::EXCEPTION, \Tracy\ILogger::CRITICAL);

exitPoint(\dumpe());
