/**
 * This file is part of the Tracy (https://tracy.nette.org)
 */

html.tracy-bs-visible,
html.tracy-bs-visible body {
	display: block;
	overflow: auto;
	position: static;
}

#tracy-bs {
	font: 9pt/1.5 Verdana, sans-serif;
	background: white;
	color: #333;
	position: absolute;
	z-index: 20000;
	left: 0;
	top: 0;
	width: 100%;
	text-align: left;
}

#tracy-bs a {
	text-decoration: none;
	color: #328ADC;
	padding: 0 4px;
	margin: 0 -4px;
}

#tracy-bs a + a {
	margin-left: 0;
}

#tracy-bs a:hover,
#tracy-bs a:focus {
	color: #085AA3;
}

#tracy-bs-toggle {
	position: absolute;
	right: .5em;
	top: .5em;
	text-decoration: none;
	background: #CD1818;
	color: white !important;
	padding: 3px;
}

#tracy-bs-toggle.tracy-collapsed {
	position: fixed;
}

.tracy-bs-main {
	display: flex;
	flex-direction: column;
	padding-bottom: 80vh;
}

.tracy-bs-main.tracy-collapsed {
	display: none;
}

#tracy-bs :where(:is(
	h1, h2, h3, h4, h5, h6,
	p,
	ol, ul, dl,
	pre, table, hr,
	.tracy-section-panel,
	.tracy-pane
):not(:first-child)) {
	margin-top: var(--tracy-space);
}

#tracy-bs h1 {
	font-size: 15pt;
	font-weight: normal;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, .3);
}

#tracy-bs h1 span {
	white-space: pre-wrap;
}

#tracy-bs h2 {
	font-size: 14pt;
	font-weight: normal;
}

#tracy-bs h3 {
	font-size: 10pt;
	font-weight: bold;
}

#tracy-bs pre,
#tracy-bs code,
#tracy-bs table {
	font: 9pt/1.5 Consolas, monospace !important;
}

#tracy-bs pre,
#tracy-bs table {
	background: #FDF5CE;
	padding: .4em .7em;
	border: 2px solid #ffffffa6;
	box-shadow: 1px 2px 6px #00000005;
	overflow: auto;
}

#tracy-bs table pre {
	padding: 0;
	margin: 0;
	border: none;
	box-shadow: none;
}

#tracy-bs table {
	border-collapse: collapse;
	width: 100%;
}

#tracy-bs td,
#tracy-bs th {
	vertical-align: top;
	text-align: left;
	padding: 2px 6px;
	border: 1px solid #e6dfbf;
}

#tracy-bs th {
	font-weight: bold;
}

#tracy-bs tr > :first-child {
	width: 20%;
}

#tracy-bs tr:nth-child(2n),
#tracy-bs tr:nth-child(2n) pre {
	background-color: #F7F0CB;
}

#tracy-bs .tracy-footer--sticky {
	position: fixed;
	width: 100%;
	bottom: 0;
}

#tracy-bs footer ul {
	font-size: 7pt;
	padding: var(--tracy-space);
	margin: var(--tracy-space) 0 0;
	color: #777;
	background: #F6F5F3;
	border-top: 1px solid #DDD;
	list-style: none;
}

#tracy-bs .tracy-footer-logo {
	position: relative;
}

#tracy-bs .tracy-footer-logo a {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 100px;
	height: 50px;
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;
	opacity: .6;
	padding: 0;
	margin: 0;
}

#tracy-bs .tracy-footer-logo a:hover,
#tracy-bs .tracy-footer-logo a:focus {
	opacity: 1;
	transition: opacity 0.1s;
}


#tracy-bs .tracy-section {
	padding: var(--tracy-space);
}

#tracy-bs .tracy-section-panel {
	background: #5040200E;
	padding: var(--tracy-space);
	border-radius: 8px;
	box-shadow: inset 1px 1px 0px 0 #00000005;
	overflow: hidden;
}

#tracy-bs .outer, /* deprecated */
#tracy-bs .tracy-pane {
	overflow: auto;
}

#tracy-bs.tracy-mac .tracy-pane {
	padding-bottom: 12px;
}


/* header */
#tracy-bs .tracy-section--error {
	background: #CD1818;
	color: white;
}

#tracy-bs .tracy-section--error p,
#tracy-bs .tracy-section--error h1 {
	font-size: 13pt;
	color: white;
}

#tracy-bs .tracy-section--error::selection,
#tracy-bs .tracy-section--error ::selection {
	color: black !important;
	background: #FDF5CE !important;
}

#tracy-bs .tracy-section--error h1 a {
	color: #ffefa1 !important;
}

#tracy-bs .tracy-section--error span span {
	font-size: 80%;
	color: rgba(255, 255, 255, 0.5);
	text-shadow: none;
}

#tracy-bs .tracy-section--error a.tracy-action {
	color: white !important;
	opacity: 0;
	font-size: .7em;
	border-bottom: none !important;
}

#tracy-bs .tracy-section--error:hover a.tracy-action {
	opacity: .6;
}

#tracy-bs .tracy-section--error a.tracy-action:hover {
	opacity: 1;
}

#tracy-bs .tracy-section--error i {
	color: #ffefa1;
	font-style: normal;
}

#tracy-bs .tracy-section--error:has(.tracy-caused) {
	border-radius: 0 0 0 8px;
	overflow: hidden;
}

#tracy-bs .tracy-caused {
	margin: var(--tracy-space) calc(-1 * var(--tracy-space)) calc(-1 * var(--tracy-space));
	padding: .3em var(--tracy-space);
	background: #df8075;
	white-space: nowrap;
}

#tracy-bs .tracy-caused a {
	color: white;
}



/* source code */
#tracy-bs pre.tracy-code > div {
	min-width: fit-content;
	white-space: pre;
}

#tracy-bs .tracy-code-comment {
	color: rgba(0, 0, 0, 0.5);
	font-style: italic;
}

#tracy-bs .tracy-code-keyword {
	color: #D24;
	font-weight: bold;
}

#tracy-bs .tracy-code-var {
	font-weight: bold;
}

#tracy-bs .tracy-line-highlight {
	background: #CD1818;
	color: white;
	font-weight: bold;
	font-style: normal;
	display: block;
	padding: 0 1ch;
	margin: 0 -1ch -1lh;
}

#tracy-bs .tracy-column-highlight {
	display: inline-block;
	backdrop-filter: grayscale(1);
	margin: 0 -1px;
	padding: 0 1px;
}

#tracy-bs .tracy-line {
	color: #9F9C7F;
	font-weight: normal;
	font-style: normal;
}

#tracy-bs a.tracy-editor {
	color: inherit;
	border-bottom: 1px dotted rgba(0, 0, 0, .3);
	border-radius: 3px;
}

#tracy-bs a.tracy-editor:hover {
	background: #0001;
}

#tracy-bs span[data-tracy-href] {
	border-bottom: 1px dotted rgba(0, 0, 0, .3);
}

#tracy-bs .tracy-dump-whitespace {
	color: #0003;
}

#tracy-bs .tracy-callstack {
	display: grid;
	overflow: auto;
	grid-template-columns: max-content 1fr;
	row-gap: calc(.5 * var(--tracy-space));
}

#tracy-bs .tracy-callstack-file {
	text-align: right;
	padding-right: var(--tracy-space);
	white-space: nowrap;
}

#tracy-bs .tracy-callstack-callee {
	white-space: nowrap;
}

#tracy-bs .tracy-callstack-additional {
	grid-column-start: 1;
	grid-column-end: 3;
}

#tracy-bs .tracy-callstack-args tr:first-child > * {
	position: relative;
}

#tracy-bs .tracy-callstack-args tr:first-child td:before {
	position: absolute;
	right: .3em;
	content: 'may not be true';
	opacity: .4;
}

#tracy-bs .tracy-panel-fadein {
	animation: tracy-panel-fadein .12s ease;
}

@keyframes tracy-panel-fadein {
	0% {
		opacity: 0;
	}
}

#tracy-bs .tracy-section--causedby {
	flex-direction: column;
	padding: 0;
}

#tracy-bs .tracy-section--causedby:not(.tracy-collapsed) {
	display: flex;
}

#tracy-bs .tracy-section--causedby .tracy-section--error {
	background: #cd1818a6;
}

#tracy-bs .tracy-section--error + .tracy-section--stack {
	margin-top: calc(1.5 * var(--tracy-space));
}


/* tabs */
#tracy-bs .tracy-tab-bar {
	display: flex;
	list-style: none;
	padding-left: 0;
	margin: 0;
	width: 100%;
	font-size: 110%;
	column-gap: var(--tracy-space);
}

#tracy-bs .tracy-tab-bar a {
	display: block;
	padding: calc(.5 * var(--tracy-space)) var(--tracy-space);
	margin: 0;
	height: 100%;
	box-sizing: border-box;
	border-radius: 5px 5px 0 0;
	text-decoration: none;
	transition: all 0.1s;
}

#tracy-bs .tracy-tab-bar > .tracy-active a {
	background: white;
}

#tracy-bs .tracy-tab-panel {
	border-top: 2px solid white;
	padding-top: var(--tracy-space);
	overflow: auto;
}
