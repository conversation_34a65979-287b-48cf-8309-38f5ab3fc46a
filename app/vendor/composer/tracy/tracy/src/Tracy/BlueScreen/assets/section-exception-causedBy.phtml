<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var \Throwable $ex
 * @var \Throwable[] $exceptions
 * @var BlueScreen $this
 * @var array[] $actions
 * @var callable $dump
 */

$ex = $ex->getPrevious();
if (!$ex || in_array($ex, $exceptions, true)) {
	return;
}
$exceptions[] = $ex;
?>

<section class="tracy-section" id="tracyCaused<?= count($exceptions) ?>">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle<?= ($collapsed = count($exceptions) > 1) ? ' tracy-collapsed' : '' ?>">Caused by</a></h2>

	<div class="tracy-section-panel tracy-section--causedby<?= $collapsed ? ' tracy-collapsed' : '' ?>">
<?php require __DIR__ . '/section-exception.phtml' ?>

	</div>
</section>
