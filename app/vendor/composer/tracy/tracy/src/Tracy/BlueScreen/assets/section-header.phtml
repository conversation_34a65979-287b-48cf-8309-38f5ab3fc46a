<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var \Throwable $ex
 * @var \Throwable[] $exceptions
 * @var array[] $actions
 * @var BlueScreen $this
 */

$title = $ex instanceof \ErrorException
	? Helpers::errorTypeToString($ex->getSeverity())
	: get_debug_type($ex);
$code = $ex->getCode() ? ' #' . $ex->getCode() : '';

?>
<section class="tracy-section tracy-section--error">
	<?php if ($ex->getMessage()): ?><p><?= Helpers::escapeHtml($title . $code) ?></p><?php endif ?>


	<h1><span><?= $this->formatMessage($ex) ?: Helpers::escapeHtml($title . $code) ?></span>
<?php foreach ($actions as $item): ?>
	<a href="<?= Helpers::escapeHtml($item['link']) ?>" class="tracy-action"<?= empty($item['external']) ? '' : ' target="_blank" rel="noreferrer noopener"'?>><?= Helpers::escapeHtml($item['label']) ?>&#x25ba;</a>
<?php endforeach ?>
	</h1>

	<?php if ($ex->getPrevious()): ?>
	<div class="tracy-caused">
		<a href="#tracyCaused<?= count($exceptions) + 1 ?>">Caused by <?= Helpers::escapeHtml(get_debug_type($ex->getPrevious())) ?></a>
	</div>
	<?php endif ?>
</section>
