<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var \Throwable $ex
 * @var \Throwable[] $exceptions
 * @var array[] $actions
 * @var callable $dump
 * @var BlueScreen $this
 * @var \Generator[] $generators
 * @var \Fiber[] $fibers
 */

?>
<?php require __DIR__ . '/section-header.phtml' ?>

<?php foreach ($this->renderPanels($ex) as $panel): ?>
<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle"><?= Helpers::escapeHtml($panel->tab) ?></a></h2>

	<div class="tracy-section-panel">
		<?= $panel->panel ?>
	</div>
</section>
<?php endforeach ?>

<?php if (!$exceptions && ($generators || $fibers)): ?>
<section class="tracy-section tracy-section--stack">
	<div class="tracy-section-panel">
		<div class="tracy-tabs">
			<ul class="tracy-tab-bar">
				<li class="tracy-tab-label tracy-active"><a href="#">Main thread</a></li>

				<?php foreach ($generators as $id => $generator): ?>
					<li class="tracy-tab-label"><a href="#">Generator #<?= $id ?></a></li>
				<?php endforeach ?>

				<?php foreach ($fibers as $id => $fiber): ?>
					<li class="tracy-tab-label"><a href="#">Fiber #<?= $id ?></a></li>
				<?php endforeach ?>
			</ul>

			<div>
				<div class="tracy-tab-panel tracy-active">
					<?php require __DIR__ . '/section-stack-exception.phtml' ?>
				</div>

				<?php foreach ($generators as $generator): ?>
					<div class="tracy-tab-panel">
						<?php require __DIR__ . '/section-stack-generator.phtml' ?>
					</div>
				<?php endforeach ?>

				<?php foreach ($fibers as $fiber): ?>
					<div class="tracy-tab-panel">
						<?php require __DIR__ . '/section-stack-fiber.phtml' ?>
					</div>
				<?php endforeach ?>
			</div>
		</div>
	</div>
</section>
<?php else: ?>
<?php require __DIR__ . '/section-stack-exception.phtml' ?>
<?php endif ?>

<?php require __DIR__ . '/section-exception-exception.phtml' ?>

<?php require __DIR__ . '/section-exception-causedBy.phtml' ?>
