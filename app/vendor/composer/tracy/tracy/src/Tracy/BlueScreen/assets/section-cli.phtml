<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var string $source
 * @var callable $dump
 */

if (!Helpers::isCli()) {
	return;
}
?>
<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">CLI request</a></h2>

	<div class="tracy-section-panel tracy-collapsed">
		<h3>Process ID <?= Helpers::escapeHtml(getmypid()) ?></h3>
<?php if (count($tmp = explode('):', $source, 2)) === 2): ?>
		<pre>php<?= Helpers::escapeHtml($tmp[1]) ?></pre>
<?php endif; ?>

<?php if (isset($_SERVER['argv'])): ?>
		<h3>Arguments</h3>
		<div class="tracy-pane">
			<table>
<?php	foreach ($_SERVER['argv'] as $k => $v): ?>
				<tr><th><?= Helpers::escapeHtml($k) ?></th><td><?= $dump($v, $k) ?></td></tr>
<?php	endforeach ?>
			</table>
		</div>
<?php endif; ?>
	</div>
</section>
