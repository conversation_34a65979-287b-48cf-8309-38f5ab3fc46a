<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var \Throwable $exception
 * @var string $title
 * @var string $nonceAttr
 * @var string $css
 * @var string $source
 */

$code = $exception->getCode() ? ' #' . $exception->getCode() : '';
$chain = Helpers::getExceptionChain($exception);
?><!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table></p></code>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title><?= Helpers::escapeHtml($title . ': ' . $exception->getMessage() . $code) ?></title>
	<!-- in <?= Helpers::escapeHtml($exception->getFile() . ':' . $exception->getLine()) ?> -->
<?php if (count($chain) > 1): ?>
<!--<?php foreach (array_slice($chain, 1) as $ex) {
	echo Helpers::escapeHtml("\n\tcaused by " . get_debug_type($ex) . ': ' . $ex->getMessage() . ($ex->getCode() ? ' #' . $ex->getCode() : ''));
} ?> -->
<?php endif ?>
	<!-- <?= Helpers::escapeHtml($source) ?> -->

	<style class="tracy-debug">
	<?= str_replace('</', '<\/', $css) ?>
	</style>
</head>


<body>
<?php require __DIR__ . '/content.phtml' ?>

<script<?= $nonceAttr ?>>
'use strict';
<?php
array_map(function ($file) { echo '(function(){', str_replace(['<!--', '</s'], ['<\!--', '<\/s'], Helpers::minifyJs(file_get_contents($file))), '})();'; }, [
	__DIR__ . '/../../assets/toggle.js',
	__DIR__ . '/../../assets/table-sort.js',
	__DIR__ . '/../../assets/tabs.js',
	__DIR__ . '/../../assets/helpers.js',
	__DIR__ . '/../../Dumper/assets/dumper.js',
	__DIR__ . '/bluescreen.js',
]);
?>
Tracy.BlueScreen.init();
</script>
</body>
</html>
