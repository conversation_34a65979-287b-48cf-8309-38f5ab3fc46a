<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var string $file
 * @var int $line
 * @var int $expanded
 */

$sourceOriginal = $file && @is_file($file) ? [$file, $line] : null; // @ - may trigger error
$sourceMapped = $sourceOriginal ? Debugger::mapSource($file, $line) : null;
?>

<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle<?= ($collapsed = $expanded !== null) ? ' tracy-collapsed' : '' ?>">Source file</a></h2>

	<div class="tracy-section-panel<?= $collapsed ? ' tracy-collapsed' : '' ?>">
<?php if ($sourceOriginal && $sourceMapped): ?>
		<div class="tracy-tabs">
			<ul class="tracy-tab-bar">
				<li class="tracy-tab-label<?= $sourceMapped['active'] ? '' : ' tracy-active' ?>"><a href="#">PHP</a></li>
				<li class="tracy-tab-label<?= $sourceMapped['active'] ? ' tracy-active' : '' ?>"><a href="#"><?= Helpers::escapeHtml($sourceMapped['label']) ?></a></li>
			</ul>

			<div>
				<div class="tracy-tab-panel<?= $sourceMapped['active'] ? '' : ' tracy-active' ?>">
					<p><b>File:</b> <?= Helpers::editorLink(...$sourceOriginal) ?></p>
					<?= BlueScreen::highlightFile(...$sourceOriginal) ?>
				</div>

				<div class="tracy-tab-panel<?= $sourceMapped['active'] ? ' tracy-active' : '' ?>">
					<p><b>File:</b> <?= Helpers::editorLink($sourceMapped['file'], $sourceMapped['line']) ?></p>
					<?= BlueScreen::highlightFile($sourceMapped['file'], $sourceMapped['line'], php: false) ?>
				</div>
			</div>
		</div>
<?php else: ?>
		<p><b>File:</b> <?= Helpers::editorLink($file, $line) ?></p>
		<?php if ($sourceOriginal) echo BlueScreen::highlightFile(...$sourceOriginal) ?>
<?php endif ?>

	</div>
</section>
