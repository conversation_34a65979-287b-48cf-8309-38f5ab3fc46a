<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var string $source
 * @var string[] $httpHeaders
 * @var callable $dump
 * @var bool $headersSent
 * @var ?string $headersFile
 * @var ?int $headersLine
 */

if (Helpers::isCli()) {
	return;
}
?>
<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">HTTP</a></h2>

	<div class="tracy-section-panel tracy-collapsed">

	<div class="tracy-tabs">
		<ul class="tracy-tab-bar">
			<li class="tracy-tab-label tracy-active"><a href="#">Request</a></li>
			<li class="tracy-tab-label"><a href="#">Response</a></li>
		</ul>

		<div>

			<div class="tracy-tab-panel tracy-active">
			<h3><?= Helpers::escapeHtml($_SERVER['REQUEST_METHOD'] ?? 'URL') ?> <a href="<?= Helpers::escapeHtml($source) ?>" target="_blank" rel="noreferrer noopener" style="font-weight: normal"><?= Helpers::escapeHtml($source) ?></a></h3>

<?php if ($httpHeaders): ?>
			<div class="tracy-pane">
				<table class="tracy-sortable">
<?php	foreach ($httpHeaders as $k => $v): ?>
					<tr><th><?= Helpers::escapeHtml($k) ?></th><td><?= $dump($v, $k) ?></td></tr>
<?php	endforeach ?>
				</table>
			</div>
<?php endif ?>


			<h3>$_GET</h3>
<?php	if (empty($_GET)):?>
			<p><i>empty</i></p>
<?php	else: ?>
			<div class="tracy-pane">
				<table class="tracy-sortable">
<?php	foreach ($_GET as $k => $v): ?>
					<tr><th><?= Helpers::escapeHtml($k) ?></th><td><?= $dump($v, $k) ?></td></tr>
<?php	endforeach ?>
				</table>
			</div>
<?php	endif ?>


<?php	if ($_SERVER['REQUEST_METHOD'] ?? null === 'POST'):?>
<?php	if (empty($_POST)):?>
<?php		if (($post = file_get_contents('php://input', length: 2000)) === ''): ?>
				<h3>$_POST</h3>
				<p><i>empty</i></p>
<?php		else: ?>
				<h3>POST (preview)</h3>
				<?= $dump($post) ?>
<?php		endif ?>
<?php	else: ?>
			<h3>$_POST</h3>
			<div class="tracy-pane">
				<table class="tracy-sortable">
<?php	foreach ($_POST as $k => $v): ?>
					<tr><th><?= Helpers::escapeHtml($k) ?></th><td><?= $dump($v, $k) ?></td></tr>
<?php	endforeach ?>
				</table>
			</div>
<?php	endif ?>
<?php	endif ?>

			<h3>$_COOKIE</h3>
<?php	if (empty($_COOKIE)):?>
			<p><i>empty</i></p>
<?php	else: ?>
			<div class="tracy-pane">
				<table class="tracy-sortable">
<?php	foreach ($_COOKIE as $k => $v): ?>
					<tr><th><?= Helpers::escapeHtml($k) ?></th><td><?= $dump($v, $k) ?></td></tr>
<?php	endforeach ?>
				</table>
			</div>
<?php	endif ?>
			</div>


			<div class="tracy-tab-panel">
			<h3>Code: <?= Helpers::escapeHtml(http_response_code()) ?></h3>
<?php if (headers_list()): ?>
			<div class="tracy-pane">
				<table class="tracy-sortable">
<?php	foreach (headers_list() as $s): $s = explode(':', $s, 2); ?>
					<tr><th><?= Helpers::escapeHtml($s[0]) ?></th><td><?= $dump(trim($s[1]), $s[0]) ?></td></tr>
<?php	endforeach ?>
				</table>
			</div>
<?php else: ?>
			<p><i>no headers</i></p>
<?php endif ?>


<?php if ($headersSent && $headersFile && @is_file($headersFile)): ?>
			<p>Headers have been sent, output started at <?= Helpers::editorLink($headersFile, $headersLine) ?> <a href="#" data-tracy-ref="^p + div" class="tracy-toggle tracy-collapsed">source</a></p>
			<div class="tracy-collapsed"><?= BlueScreen::highlightFile($headersFile, $headersLine) ?></div>
<?php elseif ($headersSent): ?>
			<p>Headers have been sent</p>
<?php else: ?>
			<p>Headers were not sent at the time the exception was thrown</p>
<?php endif ?>
			</div>
		</div>
	</div>
	</div>
</section>
