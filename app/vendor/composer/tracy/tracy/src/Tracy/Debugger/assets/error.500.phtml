<?php

/**
 * Default error page.
 */

declare(strict_types=1);

namespace <PERSON>;

/**
 * @var bool $logged
 */
?>
<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></audio></button></canvas></datalist></details></dialog></iframe></listing></meter></noembed></noframes></noscript></optgroup></option></progress></rp></select></table></template></title></video>
<meta charset="utf-8">
<meta name=robots content=noindex>
<meta name=generator content="Tracy">
<title>Server Error</title>

<style>
	#tracy-error { all: initial; position: absolute; top: 0; left: 0; right: 0; height: 70vh; min-height: 400px; display: flex; align-items: center; justify-content: center; z-index: 1000; font: 16px/1.4 sans-serif; color: #333 }
	#tracy-error * { all: initial; background: transparent; color: inherit; font: inherit }
	#tracy-error div { max-width: 550px; background: white; display: block }
	#tracy-error h1 { font: bold 50px/1.1 sans-serif; margin: 40px; display: block }
	#tracy-error p { font: 20px/1.4 sans-serif; margin: 40px; display: block }
	#tracy-error small { color: gray; font-size: 80% }
	#tracy-error small span { color: silver }
</style>

<div id=tracy-error>
	<div>
		<h1>Server Error</h1>

		<p>We're sorry! The server encountered an internal error and
		was unable to complete your request. Please try again later.</p>

		<p><small>error 500 <span> | <?php echo date('j. n. Y H:i') ?></span><?php if (!$logged): ?><br>Tracy is unable to log error.<?php endif ?></small></p>
	</div>
</div>

<script<?= Helpers::getNonceAttr() ?>>
	document.body.insertBefore(document.getElementById('tracy-error'), document.body.firstChild);
</script>
