<?php

/**
 * This file is part of the Tracy (https://tracy.nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

declare(strict_types=1);

if (!function_exists('dump')) {
	/**
	 * <PERSON>\Debugger::dump() shortcut.
	 * @tracySkipLocation
	 */
	function dump(mixed $var): mixed
	{
		array_map([<PERSON>\Debugger::class, 'dump'], func_get_args());
		return $var;
	}
}

if (!function_exists('dumpe')) {
	/**
	 * <PERSON>\Debugger::dump() & exit shortcut.
	 * @tracySkipLocation
	 */
	function dumpe(mixed $var): void
	{
		array_map([<PERSON>\Debugger::class, 'dump'], func_get_args());
		if (!Tracy\Debugger::$productionMode) {
			exit;
		}
	}
}

if (!function_exists('bdump')) {
	/**
	 * <PERSON>\Debugger::barDump() shortcut.
	 * @tracySkipLocation
	 */
	function bdump(mixed $var): mixed
	{
		<PERSON>\Debugger::barDump(...func_get_args());
		return $var;
	}
}
