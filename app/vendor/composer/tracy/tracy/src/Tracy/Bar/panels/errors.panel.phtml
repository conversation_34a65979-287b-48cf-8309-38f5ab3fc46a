<?php

declare(strict_types=1);

namespace Tracy;

/** @var int[] $data */
?>
<h1>Warnings</h1>

<div class="tracy-inner">
<table class="tracy-sortable">
<?php foreach ($data as $item => $count): [$file, $line, $message] = explode('|', $item, 3) ?>
<tr>
	<td class="tracy-right"><?= $count ? "$count\xC3\x97" : '' ?></td>
	<td><pre><?= Helpers::escapeHtml($message), ' in ', Helpers::editorLink($file, (int) $line) ?></pre></td>
</tr>
<?php endforeach ?>
</table>
</div>
