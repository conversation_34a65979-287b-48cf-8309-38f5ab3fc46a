<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var string $nonceAttr
 * @var bool $async
 * @var string $requestId
 */

$baseUrl = $_SERVER['REQUEST_URI'] ?? '';
$baseUrl .= strpos($baseUrl, '?') === false ? '?' : '&';
$asyncAttr = $async ? ' async' : '';
?>
<?php if (empty($content)): ?>
<script src="<?= Helpers::escapeHtml($baseUrl) ?>_tracy_bar=<?= urlencode("content.$requestId") ?>&amp;XDEBUG_SESSION_STOP=1" data-id="<?= Helpers::escapeHtml($requestId) ?>"<?= $asyncAttr, $nonceAttr ?>></script>
<?php else: ?>




<!-- Tracy Debug Bar -->
<script src="<?= Helpers::escapeHtml($baseUrl) ?>_tracy_bar=js&amp;v=<?= urlencode(Debugger::Version) ?>&amp;XDEBUG_SESSION_STOP=1" data-id="<?= Helpers::escapeHtml($requestId) ?>"<?= $nonceAttr ?>></script>
<script<?= $nonceAttr ?>>
Tracy.Debug.init(<?= str_replace(['<!--', '</s'], ['<\!--', '<\/s'], json_encode($content, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE)) ?>);
</script>
<?php endif ?>
