<?php

declare(strict_types=1);

namespace <PERSON>;

use <PERSON>\Helpers;

/**
 * @var string $type
 * @var \stdClass[] $panels
 */

$icons = '
	<div class="tracy-icons">
		<a href="#" data-tracy-action="window" title="open in window">&curren;</a>
		<a href="#" data-tracy-action="close" title="close window">&times;</a>
	</div>
';

echo '<div itemscope>';

foreach ($panels as $panel) {
	$content = $panel->panel ? ($panel->panel . "\n" . $icons) : '';
	$class = 'tracy-panel ' . ($type === 'ajax' ? '' : 'tracy-panel-persist') . ' tracy-panel-' . $type; ?>
	<div class="<?= $class ?>" id="tracy-debug-panel-<?= $panel->id ?>" data-tracy-content='<?= str_replace(['&', "'"], ['&amp;', '&#039;'], $content) ?>'></div><?php
}

echo '<meta itemprop=tracy-snapshot content=', Dumper::formatSnapshotAttribute(Dumper::$liveSnapshot), '>';
echo '</div>';
