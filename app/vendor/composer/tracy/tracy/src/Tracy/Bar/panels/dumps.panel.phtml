<?php

declare(strict_types=1);

namespace Tracy;

/** @var array[] $data */
?>
<style class="tracy-debug">
	#tracy-debug .tracy-DumpPanel h2 {
		font: 11pt/1.5 sans-serif;
		margin: 0;
		padding: 2px 8px;
		background: #3484d2;
		color: white;
	}
</style>

<h1>Dumps</h1>

<div class="tracy-inner tracy-DumpPanel">
<?php foreach ($data as $item): ?>
	<?php if ($item['title']):?>
	<h2><?= Helpers::escapeHtml($item['title']) ?></h2>
	<?php endif ?>

	<?= $item['dump'] ?>
<?php endforeach ?>
</div>
