<?php

declare(strict_types=1);

namespace Tracy;

/**
 * @var string $type
 * @var \stdClass[] $panels
 */
?>

<ul class="tracy-row" data-tracy-group="<?= Helpers::escapeHtml($type) ?>">
<?php if ($type === 'main'): ?>
	<li id="tracy-debug-logo" title="<PERSON>gger <?= Debugger::Version, " \nhttps://tracy.nette.org" ?>">
		<svg viewBox="0 -10 1561 333"><path fill="#585755" d="m176 327h-57v-269h-119v-57h291v57h-115v269zm208-191h114c50 0 47-78 0-78h-114v78zm106-135c17 0 33 2 46 7 75 30 75 144 1 175-13 6-29 8-47 8h-27l132 74v68l-211-128v122h-57v-326h163zm300 57c-5 0-9 3-11 9l-56 156h135l-55-155c-2-7-6-10-13-10zm-86 222l-17 47h-61l102-285c20-56 107-56 126 0l102 285h-61l-17-47h-174zm410 47c-98 0-148-55-148-163v-2c0-107 50-161 149-161h118v57h-133c-26 0-45 8-58 25-12 17-19 44-19 81 0 71 26 106 77 106h133v57h-119zm270-145l-121-181h68l81 130 81-130h68l-121 178v148h-56v-145z"/></svg>
	</li>
<?php endif; if ($type === 'redirect'): ?>
	<li><span title="Previous request before redirect">redirect</span></li>
<?php endif; if ($type === 'ajax'): ?>
	<li>AJAX</li>
<?php endif ?>

<?php foreach ($panels as $panel): if ($panel->tab) { ?>
	<li><?php if ($panel->panel): ?><a href="#" rel="tracy-debug-panel-<?= $panel->id ?>"><?= trim($panel->tab) ?></a><?php else: echo '<span>', trim($panel->tab), '</span>'; endif ?></li>
<?php } endforeach ?>

<?php if ($type === 'main'): ?>
	<li><a href="#" data-tracy-action="close" title="close debug bar">&times;</a></li>
<?php endif ?>
</ul>
