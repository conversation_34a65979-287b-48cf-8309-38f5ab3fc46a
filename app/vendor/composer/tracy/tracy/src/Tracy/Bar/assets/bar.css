/**
 * This file is part of the Tracy (https://tracy.nette.org)
 */

/* common styles */
#tracy-debug {
	--tracy-space: 10px;
	display: none;
	direction: ltr;
}

body#tracy-debug { /* in popup window */
	display: block;
}

#tracy-debug:not(body) {
	position: absolute;
	left: 0;
	top: 0;
}

#tracy-debug a {
	color: #125EAE;
	text-decoration: none;
}

#tracy-debug a:hover,
#tracy-debug a:focus {
	background-color: #125EAE;
	color: white;
}

#tracy-debug h2,
#tracy-debug h3 {
	font-weight: bold;
}

#tracy-debug :where(:is(
	h1, h2, h3, h4, h5, h6,
	p,
	ol, ul, dl,
	pre, table, hr,
	.tracy-inner,
	.tracy-inner-container,
	.tracy-inner-container > *
):not(:first-child)) {
	margin-top: var(--tracy-space);
}

#tracy-debug table {
	background: #FDF5CE;
	width: 100%;
}

#tracy-debug tr:nth-child(2n) td {
	background: rgba(0, 0, 0, 0.02);
}

#tracy-debug td,
#tracy-debug th {
	border: 1px solid #E6DFBF;
	padding: 2px 5px;
	vertical-align: top;
	text-align: left;
}

#tracy-debug th {
	background: #F4F3F1;
	color: #655E5E;
	font-size: 90%;
	font-weight: bold;
}

#tracy-debug pre,
#tracy-debug code {
	font: 9pt/1.5 Consolas, monospace;
}

#tracy-debug table .tracy-right {
	text-align: right;
}

#tracy-debug svg {
	display: inline;
}

#tracy-debug .tracy-dump {
	margin: 0;
	padding: 2px 5px;
}


/* bar */
#tracy-debug-bar {
	font: normal normal 13px/1.55 Tahoma, sans-serif;
	color: #333;
	border: 1px solid #c9c9c9;
	background: #EDEAE0 url('data:image/png;base64,R0lGODlhAQAUALMAAOzq4e/t5e7s4/Dt5vDu5e3r4vDu5uvp4O/t5AAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAAAAAAALAAAAAABABQAAAQM0EgySEAYi1LA+UcEADs=') top;
	background-size: 1em;
	position: fixed;

	min-width: 50px;
	white-space: nowrap;

	z-index: 30000;
	opacity: .9;
	transition: opacity 0.2s;
	will-change: opacity, top, left;

	border-radius: 3px;
	box-shadow: 1px 1px 10px rgba(0, 0, 0, .15);
}

#tracy-debug-bar:hover {
	opacity: 1;
	transition: opacity 0.1s;
}

#tracy-debug-bar .tracy-row {
	list-style: none none;
	display: flex;
	margin: 0;
}

#tracy-debug-bar .tracy-row:not(:first-child) {
	background: #d5d2c6;
	opacity: .8;
}

#tracy-debug-bar .tracy-row[data-tracy-group="ajax"] {
	animation: tracy-row-flash .2s ease;
}

@keyframes tracy-row-flash {
	0% {
		background: #c9c0a0;
	}
}

#tracy-debug-bar .tracy-row:not(:first-child) li:first-child {
	width: 4.1em;
	text-align: center;
}

#tracy-debug-bar img {
	vertical-align: bottom;
	position: relative;
	top: -2px;
}

#tracy-debug-bar svg {
	vertical-align: bottom;
	width: 1.23em;
	height: 1.55em;
}

#tracy-debug-bar .tracy-label {
	margin-left: .2em;
	display: inline-block; /* removes leading and trailing spaces */
}

#tracy-debug-bar li > a,
#tracy-debug-bar li > span {
	color: #000;
	display: block;
	padding: 0 .4em;
}

#tracy-debug-bar li > a:hover {
	color: black;
	background: #c3c1b8;
}

#tracy-debug-bar li:first-child {
	cursor: move;
}

#tracy-debug-logo svg {
	width: 3.4em;
	margin: 0 .2em 0 .5em;
}


/* panels */
#tracy-debug .tracy-panel {
	display: none;
	font: normal normal 12px/1.5 sans-serif;
	background: white;
	color: #333;
	text-align: left;
}

body#tracy-debug .tracy-panel { /* in popup window */
	display: block;
}

#tracy-debug h1 {
	font: normal normal 23px/1.4 Tahoma, sans-serif;
	line-height: 1;
	color: #575753;
	margin: 0;
	word-wrap: break-word;
}

#tracy-debug .tracy-inner {
	overflow: auto;
	flex: 1;
}

#tracy-debug .tracy-panel .tracy-icons {
	display: none;
}

#tracy-debug .tracy-panel-ajax h1::after,
#tracy-debug .tracy-panel-redirect h1::after {
	content: 'ajax';
	float: right;
	font-size: 65%;
	margin: 0 .3em;
}

#tracy-debug .tracy-panel-redirect h1::after {
	content: 'redirect';
}

#tracy-debug .tracy-mode-peek,
#tracy-debug .tracy-mode-float {
	position: fixed;
	flex-direction: column;
	padding: var(--tracy-space);
	min-width: 200px;
	min-height: 80px;
	border-radius: 5px;
	box-shadow: 1px 1px 20px rgba(102, 102, 102, 0.36);
	border: 1px solid rgba(0, 0, 0, 0.1);
}

#tracy-debug .tracy-mode-peek,
#tracy-debug .tracy-mode-float:not(.tracy-panel-resized) {
	max-width: 700px;
	max-height: 500px;
}

@media (max-height: 555px) {
	#tracy-debug .tracy-mode-peek,
	#tracy-debug .tracy-mode-float:not(.tracy-panel-resized) {
		max-height: 100vh;
	}
}

#tracy-debug .tracy-mode-peek h1 {
	cursor: move;
}

#tracy-debug .tracy-mode-float {
	display: flex;
	opacity: .95;
	transition: opacity 0.2s;
	will-change: opacity, top, left;
	overflow: auto;
	resize: both;
}

#tracy-debug .tracy-focused {
	display: flex;
	opacity: 1;
	transition: opacity 0.1s;
}

#tracy-debug .tracy-mode-float h1 {
	cursor: move;
	padding-right: 25px;
}

#tracy-debug .tracy-mode-float .tracy-icons {
	display: block;
	position: absolute;
	top: 0;
	right: 5px;
	font-size: 18px;
}

#tracy-debug .tracy-mode-window {
	padding: var(--tracy-space);
}

#tracy-debug .tracy-icons a {
	color: #575753;
}

#tracy-debug .tracy-icons a:hover {
	color: white;
}


#tracy-debug .tracy-inner-container {
	min-width: fit-content;
}

@media print {
	#tracy-debug * {
		display: none;
	}
}
