<?php

/**
 * This file is part of the Tracy (https://tracy.nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

declare(strict_types=1);

namespace <PERSON>\Bridges\Psr;

use Psr;
use <PERSON>;


/**
 * Psr\Log\LoggerInterface to <PERSON>\ILogger adapter.
 */
class PsrToTracyLoggerAdapter implements Tracy\ILogger
{
	/** Tracy logger level to PSR-3 log level mapping */
	private const LevelMap = [
		<PERSON>\ILogger::DEBUG => Psr\Log\LogLevel::DEBUG,
		Tracy\ILogger::INFO => Psr\Log\LogLevel::INFO,
		Tracy\ILogger::WARNING => Psr\Log\LogLevel::WARNING,
		Tracy\ILogger::ERROR => Psr\Log\LogLevel::ERROR,
		Tracy\ILogger::EXCEPTION => Psr\Log\LogLevel::ERROR,
		Tracy\ILogger::CRITICAL => Psr\Log\LogLevel::CRITICAL,
	];


	public function __construct(
		private Psr\Log\LoggerInterface $psrLogger,
	) {
	}


	public function log(mixed $value, string $level = self::INFO)
	{
		if ($value instanceof \Throwable) {
			$message = get_debug_type($value) . ': ' . $value->getMessage() . ($value->getCode() ? ' #' . $value->getCode() : '') . ' in ' . $value->getFile() . ':' . $value->getLine();
			$context = ['exception' => $value];

		} elseif (!is_string($value)) {
			$message = trim(Tracy\Dumper::toText($value));
			$context = [];

		} else {
			$message = $value;
			$context = [];
		}

		$this->psrLogger->log(
			self::LevelMap[$level] ?? Psr\Log\LogLevel::ERROR,
			$message,
			$context,
		);
	}
}
