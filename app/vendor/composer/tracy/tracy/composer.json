{"name": "tracy/tracy", "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "keywords": ["debug", "debugger", "nette", "profiler", "xdebug"], "homepage": "https://tracy.nette.org", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": "8.0 - 8.4", "ext-session": "*", "ext-json": "*"}, "require-dev": {"nette/utils": "^3.0 || ^4.0", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/tester": "^2.2", "latte/latte": "^2.5 || ^3.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "phpstan/phpstan": "^1.0"}, "conflict": {"nette/di": "<3.0"}, "autoload": {"classmap": ["src"], "files": ["src/Tracy/functions.php"]}, "minimum-stability": "dev", "scripts": {"phpstan": "phpstan analyse", "tester": "tester tests"}, "extra": {"branch-alias": {"dev-master": "2.10-dev"}}}