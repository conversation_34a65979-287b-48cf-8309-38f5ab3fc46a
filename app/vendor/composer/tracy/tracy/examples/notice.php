<?php

declare(strict_types=1);

require __DIR__ . '/../src/tracy.php';

use <PERSON>\Debugger;

// For security reasons, <PERSON> is visible only on localhost.
// You may force <PERSON> to run in development mode by passing the Debugger::Development instead of Debugger::Detect.
Debugger::enable(Debugger::Detect, __DIR__ . '/log');
Debugger::$strictMode = true;

?>
<!DOCTYPE html><link rel="stylesheet" href="assets/style.css">

<h1><PERSON> and StrictMode demo</h1>

<?php


function foo($from)
{
	echo $form;
}


foo(123);


if (Debugger::$productionMode) {
	echo '<p><b>For security reasons, <PERSON> is visible only on localhost. Look into the source code to see how to enable <PERSON>.</b></p>';
}
