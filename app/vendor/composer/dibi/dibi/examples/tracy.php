<?php
declare(strict_types=1);

if (@!include __DIR__ . '/../vendor/autoload.php') {
	die('Install dependencies using `composer install --dev`');
}


// enable <PERSON>\Debugger::enable();


$dibi = new Dibi\Connection([
	'driver' => 'sqlite',
	'database' => 'data/sample.s3db',
]);


// add panel to debug bar
$panel = new Dibi\Bridges\Tracy\Panel;
$panel->register($dibi);


// query will be logged
$dibi->query('SELECT 123');

// result set will be dumped
Tracy\Debugger::barDump($dibi->fetchAll('SELECT * FROM customers WHERE customer_id < ?', 38), '[customers]');


?>
<!DOCTYPE html><link rel="stylesheet" href="data/style.css">

<style> html { background: url(data/arrow.png) no-repeat bottom right; height: 100%; } </style>

<h1><PERSON> | dibi</h1>

<p><PERSON><PERSON> can log queries and dump variables to the <a href="https://tracy.nette.org"><PERSON></a>.</p>
