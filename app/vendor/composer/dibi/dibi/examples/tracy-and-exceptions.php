<?php
declare(strict_types=1);

if (@!include __DIR__ . '/../vendor/autoload.php') {
	die('Install dependencies using `composer install --dev`');
}


// enable <PERSON>\Debugger::enable();


$dibi = new Dibi\Connection([
	'driver' => 'sqlite',
	'database' => 'data/sample.s3db',
]);


// add panel to debug bar
$panel = new Dibi\Bridges\Tracy\Panel;
$panel->register($dibi);


// throws error because SQL is bad
$dibi->query('SELECT FROM customers WHERE customer_id < ?', 38);

?><!DOCTYPE html><link rel="stylesheet" href="data/style.css">

<h1>Tracy & SQL Exceptions | dibi</h1>

<p><PERSON><PERSON> can display and log exceptions via <a href="https://tracy.nette.org">Tracy</a>.</p>
