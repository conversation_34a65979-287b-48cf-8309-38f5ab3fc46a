<h1>Admin Panel</h1>

<h2>Users</h2>
<table>
    <thead>
        <tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Action</th></tr>
    </thead>
    <tbody>
        {foreach $users as $u}
        <tr>
            <td>{$u->id}</td>
            <td>{$u->firstname} {$u->lastname}</td>
            <td>{$u->email}</td>
            <td>{$u->role}</td>
            <td>
                {if $u->role !== 'admin'}
                <form method="post" style="display:inline;">
                    <input type="hidden" name="promote" value="{$u->id}">
                    <button type="submit">Promote to Admin</button>
                </form>
                {/if}
                {if $u->role === 'admin' && $u->id !== $user->id}
                <form method="post" style="display:inline;">
                    <input type="hidden" name="demote" value="{$u->id}">
                    <button type="submit">Demote to User</button>
                </form>
                {/if}
            </td>
        </tr>
        {/foreach}
    </tbody>
</table>

<hr>

<h2>Admin Tools</h2>
<p><a href="/filemanager" target="_blank">📁 File Manager</a> - Manage uploaded files and folders</p>
<p><a href="/upload">📤 Upload Page</a> - Go to upload interface</p>
