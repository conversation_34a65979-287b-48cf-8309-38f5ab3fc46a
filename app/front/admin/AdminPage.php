<?php namespace app\front\admin;

use app\front\layout\BaseLayout;
use app\system\AppSession;
use app\system\Redirect;
use app\front\layout\FlashMessages;
use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class AdminPage extends BaseLayout
{
    function getPageName() :string {
        return 'Admin';
    }

    protected function prepareTemplate(\app\system\component\Templater $templater): void {
        $templater->addData([
            'users' => $this->getAllUsers(),
            'user' => AppSession::getUser(),
    
        ]);
    }

    public function checkPost(array $post): void {
        $user = AppSession::getUser();

        if ($user->role !== 'admin') {
            Redirect::index();
            exit;
        }

        if (isset($post['promote']) && is_numeric($post['promote'])) {
            $targetId = (int)$post['promote'];
            dibi::query('UPDATE users SET role = %s WHERE id = %i', 'admin', $targetId);
            FlashMessages::addSuccess('User promoted to admin');
            Redirect::self();
        }

        if (isset($post['demote']) && is_numeric($post['demote'])) {
            $targetId = (int)$post['demote'];
            if ($targetId !== $user->id) { // prevent self-demotion
                dibi::query('UPDATE users SET role = %s WHERE id = %i', 'user', $targetId);
                FlashMessages::addSuccess('Admin demoted to user');
            } else {
                FlashMessages::addError('You cannot demote yourself');
            }
            Redirect::self();
        }
    }

    public array $users = [];

    public function setUsers(array $users): void {
        $this->users = $users;
    }

    /*public function getUsers(): array {
        return $this->users;
    }*/

    public function getAllUsers(): array {
        $result = dibi::fetchAll('SELECT id, firstname, lastname, email, role FROM users');
        //bdump($result); // TEMP
        return $result;
    }
}