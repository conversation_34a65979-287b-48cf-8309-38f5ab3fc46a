<?php namespace app\front\admin;

use app\front\index\IndexPage;
use app\front\dashboard\DashboardPage;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;

class AdminController extends BaseController
{
    public static function getName(): string {
        return 'admin';
    }


    public function index(Request $request): Response {

        $user = AppSession::getUser();

        if (!$user || $user->role !== 'admin') {
            if (!AppSession::getLoggedID()) {
                return new Response(
                IndexPage::renderString()
                );
            } else {
                return new Response(
                DashboardPage::renderString()
                );
            }

        }

        $page = new AdminPage();

        if ($request->getMethod() === 'POST') {
            $page->checkPost($request->request->all());
        }

        $page->setUsers($page->getAllUsers());

        //bdump($page->getUsers()); // TEMP


        return new Response(
            AdminPage::renderString()
        );
    }

   protected function getControllerMapping(): array {
      return [
         '/admin' => [$this, 'index'],
      ];
   }
}