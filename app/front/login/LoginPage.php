<?php namespace app\front\login;

use app\front\layout\BaseLayout;
use app\front\layout\FlashMessages;
use app\system\model\users\UserModel;
use app\system\model\users\LoginHelper;
use app\system\Redirect;
use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class LoginPage extends BaseLayout
{

   function getPageName() :string {
      return 'Login';
   }

   public function checkPost(array $post) :void {
      if(isset($post['btnLogin'])){
         
         $email = trim($post['email'] ?? '');
         $pwd = trim($post['pwd'] ?? '');

         if (empty($email) || empty($pwd)) {
            FlashMessages::addWarning('Fill in all fields!');
            Redirect::self();
            return;
         }

         $user = UserModel::getByEmail($email);

         if (!$user || !password_verify($pwd, $user->pwd)) {
            FlashMessages::addWarning('Incorrect login info');
            Redirect::self();
            return;
         }

         LoginHelper::loginUser($user, 'You have been logged in!');
      }
   }
}