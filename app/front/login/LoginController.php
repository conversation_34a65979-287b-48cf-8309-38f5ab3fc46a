<?php namespace app\front\login;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;
use Symfony\Component\Routing\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class LoginController extends BaseController
{
   public static function getName(): string {
      return 'login';
   }

   public function index(Request $request) :Response {
      if ($request->getMethod() === 'POST') {
         $page = new LoginPage();
         $page->checkPost($request->request->all());
      }

      return new Response(
         LoginPage::renderString()
      );
   }

   protected function getControllerMapping(): array {
      return [
         '/login' => [$this, 'index'],
      ];
   }
}