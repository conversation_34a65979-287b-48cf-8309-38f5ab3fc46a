<?php namespace app\front\register;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\BaseController;

class RegisterController extends BaseController
{
   public static function getName(): string {
      return 'register';
   }

    public function index(Request $request) :Response {
      if ($request->getMethod() === 'POST') {
         $page = new RegisterPage();
         $page->checkPost($request->request->all());
      }

      return new Response(
         RegisterPage::renderString()
      );
   }


   protected function getControllerMapping(): array {
      return [
         '/register' => [$this, 'index'],
      ];
   }
}