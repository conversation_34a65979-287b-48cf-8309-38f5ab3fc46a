<?php namespace app\front\register;

use app\front\layout\BaseLayout;
use app\front\layout\FlashMessages;
use app\system\model\users\LoginHelper;
use app\system\Redirect;
use dibi;
use app\system\component\Templater;

class RegisterPage extends BaseLayout {
    function getPageName() :string {
      return 'Register';
   }

   public function checkPost(array $post): void {
      


      if (isset($post['btnRegister'])) { // only runs when the register button is clicked
         $firstname = $post['firstname'];
         $lastname = $post['lastname'];
         $email = $post['email'];
         $pwd = $post['pwd'];

         $credentials = [$firstname, $lastname, $email, $pwd];

         if (!$firstname || !$lastname || !$email || !$pwd) {
            FlashMessages::addWarning('Fill in all fields!');
            Redirect::self();
            return;
         }
         
         foreach ($credentials as $credential) {
            if (preg_match('/\s/', $credential)) {
               FlashMessages::addWarning('No whitespaces allowed!');
               Redirect::self();
               return;
            }
         }

         if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            FlashMessages::addWarning('Invalid email format!');
            Redirect::self();
            return;
         }

         $userExists = dibi::select('id')->from('users')->where('email = %s', $email)->fetch();

         if ($userExists) {
            FlashMessages::addWarning('E-mail already in use!');
            Redirect::self();
            return;
         }

         dibi::insert('users', [
            'firstname' => $firstname,
            'lastname' => $lastname,
            'email' => $email,
            'pwd' => password_hash($pwd, PASSWORD_BCRYPT),
            'role' => 'user'
         ])->execute();
            
         // Fetch the new user's ID
         $userId = dibi::getInsertId();

         $user = (object)[
            'id' => $userId,
            'firstname' => $firstname,
            'lastname' => $lastname,
            'email' => $email,
            'role' => 'user'
         ];

         LoginHelper::loginUser($user, 'You have been registered!');
      }
   }
}