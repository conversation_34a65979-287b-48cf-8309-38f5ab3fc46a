<?php namespace app\front\projects\detail;

use app\front\layout\BaseLayout;
use app\system\component\Templater;
use app\system\model\projects\ProjectRow;
use app\system\AppSession;
use app\system\Redirect;
use app\front\layout\FlashMessages;
use app\system\model\projects\ProjectsModel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class ProjectDetail extends BaseLayout
{

   function getPageName() :string {
      return 'Project Detail';
   }

   public function setProjectRow(ProjectRow $projectRow) :ProjectDetail {
      $this->projectRow = $projectRow;
      return $this;
   }

   protected function prepareTemplate(Templater $templater) :void {
      $templater->addData([
         'project' => $this->projectRow,
         'isLogged' => !!AppSession::getLoggedID(),
      ]);
   }

   public function checkPost(array $post): void {
      if (isset($post['btnProjectEdit'])) {
         $projectID = $this->projectRow->id; // assuming $this->project is set in ProjectDetail
         $name = $post['name'] ?? null;
         $note = $post['note'] ?? '';

         if (!$name) {
            $name = $this->projectRow->name;
         }

         if (!$note) {
            $note = $this->projectRow->note;
         }
         
         if (trim($name) === '') {
            FlashMessages::addWarning('No whitespaces allowed!');
            return;
         }


         ProjectsModel::update($projectID, $name, $note);

         Redirect::to("/project/{$projectID}");
      }

      if (isset($post['btnProjectDeleteConfirm'])) {
         $projectID = $this->projectRow->id;

         ProjectsModel::delete($projectID);
         FlashMessages::addSuccess('Project deleted.');
         Redirect::index();
      }
   }

   private ProjectRow $projectRow;
}