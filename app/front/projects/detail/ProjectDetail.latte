{varType app\system\model\projects\ProjectRow $project}

<div class="viewport">
    <div class="flex justify-between">
        <div class="">
            <div class="page-title font-dm">
                {$project->getName()}
            </div>
            <div>
                {$project->created|date: 'j.n.Y'}
            </div>
        </div>

        <!-- open modal button -->
        <div class="flex gap-4">
            <button id="btnProjectEdit" class="form-section-form-button">Edit Project</button>
            <button id="btnProjectDelete" class="form-section-form-button">Delete Project</button>
        </div>

        <!-- modal backdrop + content --> <!-- EDIT PROJECT -->
        <div id="modalProjectEdit" class="modal-window">
            <div class="modal-window-content">

                <!-- close button -->
                <button id="btnCloseProjectEdit" class="modal-window-btn-close">
                    &times;
                </button>

                <!-- modal content -->
                <div>
                    <h2 class="modal-window-title font-dm">Edit Project</h2>
                    <p>Change your title or the note.</p>

                    <form method="post" class="modal-window-form">
                        <input type="text" name="name" placeholder="Title" class="form-input w-full">
                        <input name="note" placeholder="Note" class="form-input w-full">

                        <button name="btnProjectEdit" type="submit" class="form-section-form-button">Yes</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- modal backdrop + content --> <!-- DELETE CONFIRMATION -->
        <div id="modalProjectDelete" class="modal-window">
            <div class="modal-window-content">

                <!-- close button -->
                <button id="btnCloseProjectDelete" class="modal-window-btn-close">
                    &times;
                </button>

                <!-- modal content -->
                <div>
                    <h2 class="modal-window-title font-dm">Are you sure?</h2>

                    <form method="post" class="modal-window-form flex gap-4">
                        <div class="font-in">
                            <button name="btnProjectDeleteConfirm" type="submit" class="modal-window-button-confirm">Confirm</button>
                        </div>
                        <div class="font-in">
                            <button name="btnProjectDeleteCancel" class="modal-window-button-cancel">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="dashboard-section-cards">
        <div class="dashboard-section-cards-cards">
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
            <a class="dashboard-card" href="/project/{$project->id}">
                <div class="flex justify-center h-24">
                <img src="/public/img/kvmedia_logo.png" class="max-h-full object-contain">
                </div>

                <div class="mt-8">
                <p class="text-2xl font-bold">Fonts</p>
                <p class="text-gray-400 text-sm">yap about fonts</p>

                <div class="dashboard-card-badge">
                    <p class="span">badge?</p>
                </div>
                </div>
            </a>
        </div>
    </div>
</div>