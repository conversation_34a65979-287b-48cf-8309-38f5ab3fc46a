<?php namespace app\front\projects;

use app\front\projects\detail\ProjectDetail;
use app\system\model\projects\ProjectsModel;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use app\system\Redirect;
use app\system\AppSession;
use app\system\BaseController;
use Symfony\Component\Routing\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class ProjectController extends BaseController
{
   public static function getName(): string {
      return 'project';
   }

   public static function index(Request $request) :Response {
//      @TODO připravit komponentu ProjectList - šablonu jsem přesunul z dashboard.php
      return new Response(
         'TODO list detail'
      );
   }

   public function detail(Request $request, string $id): Response {
        $loggedUserId = AppSession::getLoggedID();
        $id = intval($id);

        $project = ProjectsModel::get($id);

        if (!$loggedUserId || !$project || $project->user_id !== $loggedUserId) {
            return new Response(Redirect::index());
        }

        return new Response(
            ProjectDetail::getComponent()
                ->setProjectRow($project)
                ->prepareTemplater()
                ->render()
        );
   }

   protected function getControllerMapping(): array {
        return [
            '/projects' => [$this, 'index'],
            '/project/{id}' => [$this, 'detail'],
        ];
    }
}