<?php namespace app\front\upload;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;
use Symfony\Component\Routing\Route;

class UploadController extends BaseController
{
   public static function getName(): string {
      return 'upload'; 
   }

   public function index(Request $request) :Response {
      if ($request->getMethod() === 'POST') {
         $page = new UploadPage();
         $page->checkPost($request->request->all());
      }

      return new Response(
         UploadPage::renderString()
      );
   }

   protected function getControllerMapping(): array {
      return [
         '/upload' => [$this, 'index'],
      ];
   }
}