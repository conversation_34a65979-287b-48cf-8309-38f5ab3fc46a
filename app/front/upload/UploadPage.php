<?php namespace app\front\upload;

use app\front\layout\BaseLayout;
use app\front\layout\FlashMessages;
use app\system\Redirect;
use Nette\Http\RequestFactory;
use Nette\Http\FileUpload;
use app\system\model\users\LoginHelper;
use app\system\AppSession;
use dibi;


/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class UploadPage extends BaseLayout
{

    function getPageName() :string {
        return 'Upload';
    }

    public function checkPost(array $post): void 
    {
        $user = AppSession::getUser();
        if ($user->role !== 'admin') {
            Redirect::index();
        } else {

        if (isset($post['btnUpload'])) {
            $requestFactory = new RequestFactory();
            $request = $requestFactory->fromGlobals();

            $files = $request->getFiles()['file'] ?? [];

            if (!is_array($files)) {
                $files = [$files];
            }

            $http_base_url = "https://visualbook.kvmediastudio.cz/uploads/";
            $allowed_types = ["jpg", "jpeg", "png", "gif", "pdf", "zip"];
            $uploaded_count = 0;

            foreach ($files as $file) {
                if (!$file instanceof FileUpload || !$file->isOk()) {
                    FlashMessages::addError('One of the files failed to upload');
                    continue;
                }

                $filename = str_replace(' ', '_', $file->getSanitizedName());
                $file_ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                if (!in_array($file_ext, $allowed_types)) {
                    FlashMessages::addWarning("File type not allowed: $filename");
                    continue;
                }

                $tmpPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid('upload_', true);
                $file->move($tmpPath);

                $download_url = $http_base_url . rawurlencode($filename);

                // Save to DB
                Dibi::insert('files', [
                    'filename' => $filename,
                    'filesize' => $file->getSize(),
                    'filetype' => $file->getContentType(),
                    'url' => $download_url
                ])->execute();

                $uploaded_count++;
            }

            if ($uploaded_count > 0) {
                FlashMessages::addSuccess("$uploaded_count file(s) has been uploaded");
                Redirect::download();
            } else {
                FlashMessages::addError('No files uploaded or error occurred');
                Redirect::self();
            }

            
        }
        }
    }           
}