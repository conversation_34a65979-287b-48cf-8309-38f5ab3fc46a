<?php namespace app\front\filemanager;

use app\front\layout\BaseLayout;
use app\system\Redirect;
use app\system\AppSession;
use dibi;

/** File Manager Page */
class FileManagerPage extends BaseLayout
{

    public function getPageName(): string
    {
        return 'File Manager';
    }

    protected function prepareTemplate(\app\system\component\Templater $templater): void
    {
        // Get files from database (same as DownloadPage)
        $files = dibi::query("SELECT * FROM files ORDER BY upload_date DESC")->fetchAll();

        // Calculate total file size and count
        $totalSize = 0;
        $fileTypes = [];

        foreach ($files as $file) {
            $totalSize += $file->filesize;
            $extension = strtolower(pathinfo($file->filename, PATHINFO_EXTENSION));
            $fileTypes[$extension] = ($fileTypes[$extension] ?? 0) + 1;
        }

        $templater->addData([
            'files' => $files,
            'filesCount' => count($files),
            'totalSize' => $totalSize,
            'fileTypes' => $fileTypes,
            'baseUploadUrl' => 'https://visualbook.kvmediastudio.cz/uploads/'
        ]);
    }

    public function checkPost(array $postData): void
    {
        // Handle file management actions
        $user = AppSession::getUser();
        if ($user->role !== 'admin') {
            Redirect::index();
            return;
        }

        if (isset($postData['action'])) {
            switch ($postData['action']) {
                case 'delete_file':
                    if (isset($postData['file_id'])) {
                        $this->deleteFile((int)$postData['file_id']);
                    }
                    break;

                case 'bulk_delete':
                    if (isset($postData['file_ids']) && is_array($postData['file_ids'])) {
                        $this->bulkDeleteFiles($postData['file_ids']);
                    }
                    break;
            }
        }
    }

    private function deleteFile(int $fileId): void
    {
        try {
            // Delete from database
            dibi::delete('files')->where('id = %i', $fileId)->execute();

            // Note: We don't delete from remote server as that would require additional setup
            // In a production environment, you might want to implement remote file deletion

        } catch (\Exception $e) {
            // Handle error - could add flash message here
        }
    }

    private function bulkDeleteFiles(array $fileIds): void
    {
        try {
            $fileIds = array_map('intval', $fileIds);
            dibi::delete('files')->where('id IN %in', $fileIds)->execute();

        } catch (\Exception $e) {
            // Handle error - could add flash message here
        }
    }
}
