<?php

use dibi;

/**
 * Custom elFinder volume driver for database-stored files
 * Works with VisualBook's database structure and remote file storage
 */
class elFinderVolumeDatabase extends elFinderVolumeDriver
{
    protected $baseUrl = 'https://visualbook.kvmediastudio.cz/uploads/';
    
    /**
     * Driver id
     * Must be started from letter and contains [a-z0-9]
     * Used as part of volume id
     *
     * @var string
     **/
    protected $driverId = 'db';

    /**
     * Constructor
     * Extend options with required fields
     *
     * @return void
     **/
    // public function __construct() {
    //     $opts = array(
    //         'path'          => '/',
    //         'URL'           => $this->baseUrl,
    //         'tmbPath'       => '',
    //         'tmbURL'        => '',
    //         'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
    //         'uploadDeny'    => array('all'),
    //         'uploadAllow'   => array('image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon', 'text/plain', 'application/pdf', 'application/zip'),
    //         'uploadOrder'   => array('deny', 'allow'),
    //         'accessControl' => null
    //     );
        
    //     parent::__construct();
    //     $this->options = array_merge($this->options, $opts);
    // }

    protected function init() {
        $this->options = array_merge(array(
            'baseUrl'       => '',
            'tmbPath'       => '',
            'tmbURL'        => '',
            'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
            'uploadDeny'    => array('all'),
            'uploadAllow'   => array(
                'image/x-ms-bmp',
                'image/gif',
                'image/jpeg',
                'image/png',
                'image/x-icon',
                'text/plain',
                'application/pdf',
                'application/zip'
            ),
            'uploadOrder'   => array('deny', 'allow'),
            'accessControl' => null
        ), $this->options);

        return parent::init();
    }

    /**
     * Configure after successfull mount.
     *
     * @return void
     **/
    protected function configure() {
        parent::configure();
        
        // Set root hash
        $this->root = $this->encode('/');
    }

    /**
     * Return parent directory path
     *
     * @param  string  $path  file path
     * @return string
     **/
    protected function _dirname($path) {
        return '/';
    }

    /**
     * Return file name
     *
     * @param  string  $path  file path
     * @return string
     **/
    protected function _basename($path) {
        if ($path === '/') {
            return $this->options['alias'] ?: 'Files';
        }
        
        // Extract filename from database ID
        $id = $this->getIdFromPath($path);
        if ($id) {
            $file = dibi::query("SELECT filename FROM files WHERE id = %i", $id)->fetch();
            return $file ? $file->filename : 'unknown';
        }
        
        return 'unknown';
    }

    /**
     * Join dir name and file name and return full path
     *
     * @param  string  $dir
     * @param  string  $name
     * @return string
     **/
    protected function _joinPath($dir, $name) {
        return '/' . $name;
    }

    /**
     * Return normalized path, this works the same as os.path.normpath() in Python
     *
     * @param  string  $path  path
     * @return string
     **/
    protected function _normpath($path) {
        return $path === '' ? '/' : $path;
    }

    /**
     * Return file path related to root dir
     *
     * @param  string  $path  file path
     * @return string
     **/
    protected function _relpath($path) {
        return $path;
    }

    /**
     * Convert path related to root dir into real path
     *
     * @param  string  $path  file path
     * @return string
     **/
    protected function _abspath($path) {
        return $path;
    }

    /**
     * Return true if $path is children of $parent
     *
     * @param  string  $path    path to check
     * @param  string  $parent  parent path
     * @return bool
     **/
    protected function _inpath($path, $parent) {
        return $parent === '/';
    }

    /**
     * Return stat for given path.
     * Stat contains following fields:
     * - (int)    size    file size in b. required
     * - (int)    ts      file modification time in unix time. required
     * - (string) mime    mimetype. required for folders, others - optionally
     * - (bool)   read    read permissions. required
     * - (bool)   write   write permissions. required
     * - (bool)   locked  is object locked. optionally
     * - (bool)   hidden  is object hidden. optionally
     * - (string) alias   for symlinks - link target path relative to root path. optionally
     * - (string) target  for symlinks - link target path. optionally
     *
     * If file does not exists - return false
     *
     * @param  string  $path  file path
     * @return array|false
     **/
    protected function _stat($path) {
        if ($path === '/') {
            return array(
                'size'  => 0,
                'ts'    => time(),
                'mime'  => 'directory',
                'read'  => true,
                'write' => true,
                'locked' => false,
                'hidden' => false
            );
        }

        $id = $this->getIdFromPath($path);
        if (!$id) {
            return false;
        }

        $file = dibi::query("SELECT * FROM files WHERE id = %i", $id)->fetch();
        if (!$file) {
            return false;
        }

        return array(
            'size'  => $file->filesize,
            'ts'    => strtotime($file->upload_date),
            'mime'  => $file->filetype,
            'read'  => true,
            'write' => true,
            'locked' => false,
            'hidden' => false
        );
    }

    /**
     * Return true if path is dir and has at least one childs directory
     *
     * @param  string  $path  dir path
     * @return bool
     **/
    protected function _subdirs($path) {
        return false; // No subdirectories in our flat file structure
    }

    /**
     * Return object width and height
     * Usualy used for images, but can be realize for video etc...
     *
     * @param  string  $path  file path
     * @param  string  $mime  file mime type
     * @return string
     **/
    protected function _dimensions($path, $mime) {
        return '';
    }

    /**
     * Return files list in directory.
     *
     * @param  string  $path  dir path
     * @return array
     **/
    protected function _scandir($path) {
        if ($path !== '/') {
            return array();
        }

        $files = dibi::query("SELECT id FROM files ORDER BY upload_date DESC")->fetchAll();
        $result = array();

        foreach ($files as $file) {
            $result[] = '/file_' . $file->id;
        }

        return $result;
    }

    /**
     * Open file and return file pointer
     *
     * @param  string  $path  file path
     * @param  string  $mode  open file mode (ignored in this implementation)
     * @return resource|false
     **/
    protected function _fopen($path, $mode = 'rb') {
        $id = $this->getIdFromPath($path);
        if (!$id) {
            return false;
        }

        $file = dibi::query("SELECT url FROM files WHERE id = %i", $id)->fetch();
        if (!$file) {
            return false;
        }

        // Return remote file stream
        return fopen($file->url, 'rb');
    }

    /**
     * Close opened file
     *
     * @param  resource  $fp  file pointer
     * @param  string    $path file path
     * @return bool
     **/
    protected function _fclose($fp, $path = '') {
        return fclose($fp);
    }

    /**
     * Create dir and return created dir path or false on failed
     *
     * @param  string  $path  parent dir path
     * @param  string  $name  new directory name
     * @return string|bool
     **/
    protected function _mkdir($path, $name) {
        return false; // Directory creation not supported
    }

    /**
     * Create file and return it's path or false on failed
     *
     * @param  string  $path  parent dir path
     * @param  string  $name  new file name
     * @return string|bool
     **/
    protected function _mkfile($path, $name) {
        return false; // File creation not supported through elFinder
    }

    /**
     * Remove file
     *
     * @param  string  $path  file path
     * @return bool
     **/
    protected function _unlink($path) {
        $id = $this->getIdFromPath($path);
        if (!$id) {
            return false;
        }

        try {
            dibi::delete('files')->where('id = %i', $id)->execute();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove dir
     *
     * @param  string  $path  dir path
     * @return bool
     **/
    protected function _rmdir($path) {
        return false; // Directory removal not supported
    }

    /**
     * Extract file ID from path
     *
     * @param  string  $path
     * @return int|false
     **/
    protected function getIdFromPath($path) {
        if (preg_match('/\/file_(\d+)$/', $path, $matches)) {
            return (int)$matches[1];
        }
        return false;
    }

    /**
     * Return file path
     *
     * @param  string  $path  file path
     * @return string
     **/
    protected function _path($path) {
        return $path;
    }

    /**
     * Create symlink
     *
     * @param  string  $source     source file path
     * @param  string  $targetDir  target directory path
     * @param  string  $name       symlink name
     * @return bool
     **/
    protected function _symlink($source, $targetDir, $name) {
        return false; // Symlinks not supported
    }

    /**
     * Copy file into another file
     *
     * @param  string  $source     source file path
     * @param  string  $targetDir  target directory path
     * @param  string  $name       new file name
     * @return bool
     **/
    protected function _copy($source, $targetDir, $name) {
        return false; // Copy not supported
    }

    /**
     * Move file into another parent dir.
     * Return new file path or false.
     *
     * @param  string  $source  source file path
     * @param  string  $target  target dir path
     * @param  string  $name    file name
     * @return string|bool
     **/
    protected function _move($source, $target, $name) {
        return false; // Move not supported
    }

    /**
     * Create new file and write into it from file pointer.
     * Return new file path or false on error.
     *
     * @param  resource  $fp   file pointer
     * @param  string    $dir  target dir path
     * @param  string    $name file name
     * @param  array     $stat file stat (required by some virtual fs)
     * @return bool|string
     **/
    protected function _save($fp, $dir, $name, $stat) {
        return false; // Save not supported
    }

    /**
     * Get file contents
     *
     * @param  string  $path  file path
     * @return string|false
     **/
    protected function _getContents($path) {
        $fp = $this->_fopen($path);
        if (!$fp) {
            return false;
        }

        $contents = stream_get_contents($fp);
        $this->_fclose($fp, $path);

        return $contents;
    }

    /**
     * Write a string to a file
     *
     * @param  string  $path     file path
     * @param  string  $content  new file content
     * @return bool
     **/
    protected function _filePutContents($path, $content) {
        return false; // File editing not supported
    }

    /**
     * Detect available archivers
     *
     * @return void
     **/
    protected function _checkArchivers() {
        // No archivers supported
    }

    /**
     * Unpack archive
     *
     * @param  string  $path  archive path
     * @param  array   $arc   archiver command and arguments (same as in $this->archivers)
     * @return void
     **/
    protected function _unpack($path, $arc) {
        // Unpacking not supported
    }

    /**
     * Recursive symlinks search
     *
     * @param  string  $path  file/dir path
     * @return bool
     **/
    protected function _findSymlinks($path) {
        return false; // No symlinks
    }

    /**
     * Extract files from archive
     *
     * @param  string  $path  archive path
     * @param  array   $arc   archiver command and arguments (same as in $this->archivers)
     * @return bool
     **/
    protected function _extract($path, $arc) {
        return false; // Extraction not supported
    }

    /**
     * Create archive and return its path
     *
     * @param  string  $dir    target dir
     * @param  array   $files  files names list
     * @param  string  $name   archive name
     * @param  array   $arc    archiver options
     * @return string|bool
     **/
    protected function _archive($dir, $files, $name, $arc) {
        return false; // Archive creation not supported
    }

    /**
     * Change file mode (chmod)
     *
     * @param  string  $path  file path
     * @param  string  $mode  octal string such as '0755'
     * @return bool
     **/
    protected function _chmod($path, $mode) {
        return false; // Chmod not supported
    }
}
