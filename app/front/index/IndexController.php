<?php namespace app\front\index;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\Redirect;
use app\system\AppSession;
use app\system\model\users\UserModel;
use app\front\dashboard\DashboardPage;
use app\system\BaseController;
use Symfony\Component\Routing\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class IndexController extends BaseController
{
   public static function getName(): string {
      return 'index';
   }

   public function index(Request $request): Response {
        if (!AppSession::getLoggedID()) {
            return new Response(
                IndexPage::renderString()
            );
        }

        return new Response(
            DashboardPage::renderString()
        );
    }

   protected function getControllerMapping(): array {
        return [
            '/' => [$this, 'index'],
        ];
    }
}