<?php namespace app\front\index;

use app\front\layout\BaseLayout;
use app\system\AppSession;
use app\system\component\Templater;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class IndexPage extends BaseLayout
{

   function getPageName() :string {
      return 'Úvodní stránka';
   }

   protected function prepareTemplate(Templater $templater) :void {
      $templater->addData([
         'isLogged' => !!AppSession::getLoggedID(),
      ]);
   }
}