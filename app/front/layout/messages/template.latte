{varType \app\front\layout\Message[] $messages}

{* !!!Prototyp vygenerovaný chatem *}

<div id="toast" class="fixed top-4 left-1/2 transform -translate-x-1/2 translate-y-[-100%] transition-all duration-300 pointer-events-none z-50">
   <div id="toast-inner" class="font-dm">
      <span id="toast-msg" class="flex-1"></span>
      <button id="toast-close"></button>
   </div>
</div>

<script>
   function showToast(msg, type = 'success', duration = 3000) {
      const toast = document.getElementById('toast');
      const inner = document.getElementById('toast-inner');
      const text = document.getElementById('toast-msg');

      inner.classList.remove('bg-green-500','bg-red-500','bg-blue-500','bg-yellow-500');
      switch (type) {
         case 'error':   inner.classList.add('flash-message-red'); break;
         case 'info':    inner.classList.add('flash-message-blue'); break;
         case 'warning': inner.classList.add('flash-message-yellow'); break;
         default:        inner.classList.add('flash-message-green');
      }

      text.textContent = msg;

      toast.classList.remove('opacity-0', 'translate-y-[-100%]');
      toast.classList.add('opacity-100', 'translate-y-0');
      toast.classList.remove('pointer-events-none');

      clearTimeout(toast._hideTimeout);
      toast._hideTimeout = setTimeout(hideToast, duration);
   }

   function hideToast() {
      const toast = document.getElementById('toast');
      toast.classList.add('opacity-0', 'translate-y-[-100%]');
      toast.classList.remove('opacity-100', 'translate-y-0');
      toast._hideTimeout = null;
   }

   document.getElementById('toast-close').addEventListener('click', hideToast);

   window.appFlashMessages = {
      showAll: function() {
         const messages = {$messages|json_encode|noescape};
         for (const m of messages) {
            showToast(m.content, m.type);
         }
      },
      setMessage: function(msg) {
         showToast(msg.content, msg.type);
      },
      setSuccess: function(msg) {
         showToast(msg, 'success');
      },
      setError: function(msg) {
         showToast(msg, 'error');
      },
      setInfo: function(msg) {
         showToast(msg, 'info');
      },
      setWarning: function(msg) {
         showToast(msg, 'warning');
      }
   };

   window.addEventListener('DOMContentLoaded', () => {
      window.appFlashMessages.showAll();
   });
</script>
