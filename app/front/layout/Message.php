<?php namespace app\front\layout;

class Message {
    public string $type;
    public string $content;

    public function __construct(string $type, string $content) {
        $this->type = $type;
        $this->content = $content;
    }

    public function toArray(): array {
        return [
            'type' => $this->type, 
            'content' => $this->content
        ];
    }

    public static function fromArray(array $data): self {
        return new self($data['type'], $data['content']);
    }
}