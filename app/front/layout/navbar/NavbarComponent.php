<?php namespace app\front\layout\navbar;

use app\system\component\Component;
use app\system\AppSession;
use app\system\Redirect;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class NavbarComponent extends Component
{

   function setData() :array {
      $user = AppSession::getUser();

      if (!$user) {
         return [
         'isLogged' => false
         ];
      }
      
      return [
         'isLogged' => true,
         'user' => $user
      ];      
   }

   public function checkPost(array $post) :void {
        if(isset($post['btnLogout'])){
         
            //session_start();
            session_unset();
            session_destroy();

            Redirect::login();
            exit;
        }
    }
}