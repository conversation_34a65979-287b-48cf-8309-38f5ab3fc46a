<?php namespace app\front\layout;

use app\front\layout\messages\SessionMessagesComponent;
use app\front\layout\navbar\NavbarComponent;
use app\system\component\Component;
use app\system\component\Templater;
use Exception;
use ReflectionClass;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
abstract class BaseLayout extends Component
{

   abstract function getPageName() :string;

   protected function prepareTemplate(Templater $templater) :void { }

   protected function prepareFromString() :?string {
      return null;
   }

   final public static function renderString() :string {
      return self::getComponent()->__toString();
   }

   final function setData() :array { // get variables to use in latte layout
      $this->data['navbar'] = NavbarComponent::getComponent()->renderHtml();
      $this->prepare();

      $this->data['pageName'] = $this->getPageName();


      $this->data['messagesComponent'] = SessionMessagesComponent::getComponent()->renderHtml();

      return $this->data;
   }

   final protected function getTemplatePath(string $templateName) :string {
      return __DIR__ . '/_layout.latte';
   }

   final protected function getTemplateFile(string $className) :string {
      $refl = new ReflectionClass($className);

      if(is_file($file = dirname($refl->getFileName()) . '/' . $refl->getShortName() . '.latte'))
         $templateFile = $file;
      else
         throw new Exception('Template panelu nebyl nalezen');

      return $templateFile;
   }

   final protected function setContentData() :void {
      if($this->prepareFromString()){
         $this->data['content'] = $this->prepareFromString();
         return;
      }

      if($template = $this->getTemplateFile(static::class)){
         $templater = Templater::prepare($template);
         $this->prepareTemplate($templater);
         $this->data['content'] = $templater->render();
      }
   }

   private function prepare() :void {
      $this->setContentData();
   }
}