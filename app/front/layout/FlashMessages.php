<?php namespace app\front\layout;

//use app\front\layout\Message;
//@TODO změnit namespace app\system\messages
class FlashMessages
{
    const SESSION_KEY = 'errors';

//    @TODO připravit třídu která bude přesně pro jednu zprávu, bude obsahovat
//    1. typ zprávy (success, error, info)
//    2. obsah zprávy
//    3. definovat parametr v add()
//       - public static function add(Message $messages): void

    public static function add(Message|array $messages): void {
        if (!isset($_SESSION[self::SESSION_KEY])) {
            $_SESSION[self::SESSION_KEY] = [];
        }

        if (is_array($messages)) {
            foreach ($messages as $message){
                $_SESSION[self::SESSION_KEY][] = $message->toArray();
            }
        } else {
            $_SESSION[self::SESSION_KEY][] = $messages->toArray();
        }
    }

    public static function addSuccess(string $content): void {
        self::add(new Message('success', $content));
    }

    public static function addError(string $content): void {
        self::add(new Message('error', $content));
    }

    public static function addWarning(string $content): void {
        self::add(new Message('warning', $content));
    }

    public static function addInfo(string $content): void {
        self::add(new Message('info', $content));
    }
 
//    4. po úpravě tato funkce bude vracet pole Message[]
    /**
     * @return Message[]
     */
    public static function getAndClean(): array {
        if (!isset($_SESSION[self::SESSION_KEY])) {
            return [];
        }
        $rawMessages = $_SESSION[self::SESSION_KEY];
        unset($_SESSION[self::SESSION_KEY]);
        return array_map(fn($m) => Message::fromArray($m), $rawMessages);
    }
}