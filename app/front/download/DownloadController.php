<?php namespace app\front\download;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;

class DownloadController extends BaseController
{
   public static function getName(): string {
      return 'download';
   }

   public function index(Request $request) :Response {
      if ($request->getMethod() === 'POST') {
         $page = new DownloadPage();
         $page->checkPost($request->request->all());
      }

      return new Response(
         DownloadPage::renderString()
      );
   }

   protected function getControllerMapping(): array {
      return [
         '/download' => [$this, 'index'],
      ];
   }
}