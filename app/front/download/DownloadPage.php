<?php namespace app\front\download;

use app\front\layout\BaseLayout;
use app\system\Redirect;
use app\system\AppSession;
use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class DownloadPage extends BaseLayout
{

    public function getPageName(): string
    {
        return 'Download';
    }

    protected function prepareTemplate(\app\system\component\Templater $templater): void 
    {
        $files = dibi::query("SELECT * FROM files")->fetchAll();
        $templater->addData([
            'files' => $files,
        ]);    
    }
}