<div class="viewport">
    <div class="page-title"><!-- title -->
        <h1 class="font-dm">Download Files</h1>
    </div>

    <table class="border-separate border-spacing-2">
        <thead>
            <tr>
                <th class="text-left bg-red-500 border border-black border-2">File Name</th>
                <th class="text-left">File Size</th>
                <th class="text-left">Type</th>
                <th class="text-left">Download</th>
            </tr>
        </thead>
        <tbody>
            {if $files}
                {foreach $files as $file}
                    <tr class="bg-cyan-500">
                        <td class="border border-black">{$file['filename']|escape}</td>
                        <td class="border border-black bg-green-500">{$file['filesize']} bytes</td>
                        <td class="border border-black bg-green-500">{$file['filetype']}</td>
                        <td class="border border-black bg-green-500"><a href="{htmlspecialchars($file['url'])}" download>Download</a></td>
                    </tr>
                {/foreach}
                <tr>
                    <td colspan="4">
                        Upload another file: <button><a href="/upload">Upload</a></button>
                    </td>
                </tr>
            {else}
                <tr><td colspan="4">No files uploaded.</td></tr>
            {/if}
        </tbody>
    </table>
</div>