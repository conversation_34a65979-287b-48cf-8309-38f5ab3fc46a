<?php namespace app\front\dashboard;

use app\front\layout\BaseLayout;
use app\front\login\LoginPage;
use app\system\AppSession;
use app\system\component\Templater;
use app\system\Redirect;
use app\system\model\projects\ProjectsModel;
use app\front\layout\FlashMessages;
use app\front\projects\projectslist\ProjectsList;



/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class DashboardPage extends BaseLayout
{
   function getPageName() :string {
      return 'Dashboard';
   }

   protected function prepareTemplate(Templater $templater) :void {
      $prompts = [
        "Choose a brand to manage.",
        "Your projects, all in one place.",
        "What are we working with today?",
        "Select a company to get started."
      ];

      $prompt = $prompts[array_rand($prompts)];

      $templater->addData([
         'isLogged' => !!AppSession::getLoggedID(),
         'userName' => AppSession::getUserName(),
         'prompt' => $prompt,
         'projectsList' => ProjectsList::getComponent()->renderHtml(),
      ]);
   }

   public function checkPost(array $post): void {
      if (isset($post['btnProjectNew'])) {
         $name = $post['name'] ?? null;
         $note = $post['note'] ?? '';
         $userID = AppSession::getLoggedID();

         if (!$name || trim($name) === '') {
            FlashMessages::addWarning('Fill in all required fields!');
            return;
         }

         $id = ProjectsModel::create($name, $note, $userID);

         Redirect::index();
      }
   }
}