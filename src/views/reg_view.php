<?php

declare(strict_types=1);

function reg_input() {

    if (isset($_SESSION["reg_data"]["firstname"])) {
        echo '<input class="form-section-form-input" type="text" name="firstname" placeholder="Firstname" value="' . $_SESSION["reg_data"]["firstname"] . '">';
    }
    else {
        echo '<input class="form-section-form-input" type="text" name="firstname" placeholder="Firstname">';
    }

    if (isset($_SESSION["reg_data"]["lastname"])) {
        echo '<input class="form-section-form-input" type="text" name="lastname" placeholder="Lastname" value="' . $_SESSION["reg_data"]["lastname"] . '">';
    }
    else {
        echo '<input class="form-section-form-input" type="text" name="lastname" placeholder="Lastname">';
    }

    echo '<input class="form-section-form-input" type="password" name="pwd" placeholder="Password">';

    if (isset($_SESSION["reg_data"]["email"]) && !isset($_SESSION["errors_reg"]["email_used"]) && !isset($_SESSION["errors_reg"]["invalid_email"])) {
        echo '<input class="form-section-form-input" type="text" name="email" placeholder="E-Mail" value="' . $_SESSION["reg_data"]["email"] . '">';
    }
    else {
        echo '<input class="form-section-form-input" type="text" name="email" placeholder="E-Mail">';
    }
}

function checkRegErrors() {
    if (isset($_SESSION['errors_reg'])) {
        $errors = $_SESSION['errors_reg'];

        echo "<br>";

        foreach ($errors as $error) {
            echo '<p class="text-red-500">' . $error . '</p>';
        }

        unset($_SESSION['errors_reg']);
    }
    else if (isset($_GET["registration"]) && $_GET["registration"] === "success") {
        echo '<br>';
        echo '<p class="text-green-500">Registration success!</p>';
    }
}
