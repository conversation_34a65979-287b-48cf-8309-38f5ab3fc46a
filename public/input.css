@import "tailwindcss";

@import "tailwindcss/preflight";
@import "tailwindcss/utilities";


/*
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
*/

@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;700&display=swap');


@import url('https://fonts.googleapis.com/css2?family=Chivo+Mono:wght@400;700&display=swap');


@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');


/* 
~~~~~~~~~~~
G L O B A L
~~~~~~~~~~~
*/

/*
====
FUNC
====
*/

.cursor-pointer {
    @apply cursor-pointer;
}

/*
====
PAGE
====
*/

.viewport {
    @apply h-screen px-20;
}

.page {
    @apply mt-8;
}

.pagebreak {
    @apply border-t border-dotted border-black w-full h-px my-8;
}

/*
=====
FONTS
=====
*/

/*
@layer base { // for default font
  body {
    font-family: 'DM Sans', sans-serif;
  }
}
*/

.font-in {
    font-family: 'Inter', sans-serif;
}

.font-dm {
    font-family: 'DM Sans', sans-serif;
}

@layer utilities {
  .font-chivo {
    font-family: "Chivo Mono", monospace;
  }
}

/*
========
ELEMENTS
========
*/

/* NAVBAR */

.navbar {
    @apply flex items-center justify-between px-8 py-10 mb-16 border-hidden sticky shadow-lg;
}

.navbar-img {
    @apply object-cover w-full h-12;
}

.navbar-button {
    @apply flex items-center cursor-pointer bg-[#ffd400] border border-[#ffd400] text-sm sm:text-base py-3 px-6 rounded-full transform hover:-translate-y-1 hover:bg-white hover:border-black hover:text-black hover:shadow-lg active:translate-y-1 transition ease-out duration-400;
}

/* FLASH MESSAGES */
.flash-message-red {
    @apply flex bg-red-50 border border-red-500 py-3 px-4 rounded-xl shadow-xl shadow-red-500/20 lowercase;
}

.flash-message-blue {
    @apply flex bg-blue-50 border border-blue-500 py-3 px-4 rounded-xl shadow-xl shadow-blue-500/20 lowercase;
}

.flash-message-yellow {
    @apply flex bg-yellow-50 border border-yellow-500 py-3 px-4 rounded-xl shadow-xl shadow-yellow-500/20 lowercase;
}

.flash-message-green {
    @apply flex bg-green-50 border border-green-500 py-3 px-4 rounded-xl shadow-xl shadow-green-500/20 lowercase;
}

/* FORM */

.form-section {
    @apply flex flex-col items-center mt-16 mb-16 px-8;
}

.form-section-form {
    @apply flex flex-col items-center bg-gray-100 mt-8 rounded-3xl p-12 gap-4;
}

.form-section-form-input {
    @apply bg-gray-200 p-4 rounded-xl;
}

.form-section-form-button {
    @apply cursor-pointer bg-gray-200 mt-4 rounded-xl border border-gray-400 p-4 w-44 hover:bg-white hover:-translate-y-1 hover:shadow-lg hover:border-black active:translate-y-1 transition ease-out duration-400;
}

/* MODAL WINDOW */

.modal-window {
  @apply fixed inset-0 bg-black/50 flex items-center justify-center z-50 opacity-0 pointer-events-none transition-opacity transition-transform duration-200 ease-out;
}

.modal-window.open {
  @apply opacity-100 scale-100 pointer-events-auto;
}

.modal-window-content {
    @apply bg-white p-6 rounded-lg shadow-xl w-full max-w-2xl max-h-1/2 relative;
}

.modal-window-btn-close {
    @apply absolute cursor-pointer top-2 right-2 size-7 text-gray-600 hover:text-black text-2xl font-bold ease-out duration-400;
}

.modal-window-title {
    @apply text-4xl;
}

.modal-window-form {
    @apply space-y-2 mt-4;
}

.modal-window-button-confirm {
    @apply cursor-pointer bg-gray-50 border border-2 text-green-500 font-bold shadow-green-200 rounded-2xl p-4 border-green-500 transform hover:-translate-y-1 hover:bg-white hover:border-green-500 hover:text-green-500 hover:shadow-lg hover:shadow-green-300/50 active:translate-y-1 active:shadow-none transition ease-out duration-500;
}

.modal-window-button-cancel {
    @apply cursor-pointer bg-gray-50 border border-2 text-red-500 font-bold shadow-red-200 rounded-2xl p-4 border-red-500 transform hover:-translate-y-1 hover:bg-white hover:border-red-500 hover:text-red-500 hover:shadow-lg hover:shadow-red-300/50 active:translate-y-1 active:shadow-none transition ease-out duration-500;
}

/*
========
HEADINGS
========
*/

.page-title {
    @apply text-7xl font-black;
}

.form-title {
    @apply text-4xl;
}


/*
~~~~~~~~~~~~~~~~~~~~~~~~~~~
S P E C I F I C   P A G E S
~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

/*
---------
INDEX.PHP 
---------
*/

.index-title {
    @apply flex text-9xl font-black px-10;
}

.index-description {
    @apply mt-8 text-2xl font-normal px-10;
}


/* 
-------------
DASHBOARD.PHP 
-------------
*/

.dashboard-section-welcome {
    @apply mt-8;
}

.dashboard-section-prompt {
    @apply mt-4;
}

.dashboard-section-cards {
    @apply mt-24 mb-16 px-8;
}

.dashboard-section-cards-title {
    @apply flex text-7xl justify-center px-8;
}

.dashboard-section-cards-cards {
    @apply grid place-items-center justify-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-20 mt-16;
}

.dashboard-card {
    @apply flex flex-col bg-gray-50 border border-black hover:border-[#ffd400] rounded-3xl p-8 cursor-pointer overflow-hidden shadow-md w-72 aspect-6/7 transform transition ease-out duration-500 hover:-translate-y-1 hover:shadow-none;
}

.dashboard-card-text {
    @apply flex flex-col flex-grow;
}

.dashboard-card-badge {
    @apply bg-gray-200 flex justify-center mt-auto max-w-24 px-4 py-2 rounded-xl opacity-50 transition hover:opacity-100 ease-out duration-300;
}

/* 
----------------
REGISTRATION.PHP 
----------------
*/
