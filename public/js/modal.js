document.addEventListener('DOMContentLoaded', () => {
  // VAR PROJECT NEW
  const modalProjectNew = document.getElementById('modalProjectNew')
  const btnProjectNew = document.getElementById('btnProjectNew')
  const btnCloseProjectNew = document.getElementById('btnCloseProjectNew')

  // VAR PROJECT EDIT
  const modalProjectEdit = document.getElementById('modalProjectEdit')
  const btnProjectEdit = document.getElementById('btnProjectEdit')
  const btnCloseProjectEdit = document.getElementById('btnCloseProjectEdit')
  
  // VAR PROJECT DELETE
  const modalProjectDelete = document.getElementById('modalProjectDelete')
  const btnProjectDelete = document.getElementById('btnProjectDelete')
  const btnProjectDeleteConfirm = document.getElementById('btnProjectDeleteConfirm')
  const btnProjectDeleteCancel = document.getElementById('btnProjectDeleteCancel')
  const btnCloseProjectDelete = document.getElementById('btnCloseProjectDelete')

  // MODAL PROJECT NEW

    btnProjectNew?.addEventListener('click', () => {
        modalProjectNew.classList.add('open');
    });

    btnCloseProjectNew?.addEventListener('click', () => {
        modalProjectNew.classList.remove('open');
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            modalProjectNew.classList.remove('open');
        }
    })

  // MODAL PROJECT EDIT

    btnProjectEdit?.addEventListener('click', () => {
        modalProjectEdit.classList.add('open');
    });

    btnCloseProjectEdit?.addEventListener('click', () => {
        modalProjectEdit.classList.remove('open');
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            modalProjectEdit.classList.remove('open');
        }
    })

  // MODAL PROJECT DELETE

    btnProjectDelete?.addEventListener('click', () => {
        modalProjectDelete.classList.add('open');
    });

    btnCloseProjectDelete?.addEventListener('click', () => {
        modalProjectDelete.classList.remove('open');
    });

    btnProjectDeleteConfirm?.addEventListener('click', () => {
        modalProjectDelete.classList.remove('open');
    });

    btnProjectDeleteCancel?.addEventListener('click', () => {
        modalProjectDelete.classList.remove('open');
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            modalProjectDelete.classList.remove('open');
        }
    })
})
