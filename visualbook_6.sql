-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4deb2+deb11u2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 01, 2025 at 11:49 AM
-- Server version: 10.5.29-MariaDB-0+deb11u1
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `visualbook_6`
--

-- --------------------------------------------------------

--
-- Table structure for table `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `filename` varchar(200) NOT NULL,
  `filesize` int(11) NOT NULL,
  `filetype` varchar(100) NOT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `url` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

--
-- Dumping data for table `files`
--

INSERT INTO `files` (`id`, `filename`, `filesize`, `filetype`, `upload_date`, `url`) VALUES
(6, 'John-the-Ripper-The-Basics-1728574618398.zip', 14368, 'application/zip', '2025-05-13 12:12:53', 'https://visualbook.kvmediastudio.cz/uploads/John-the-Ripper-The-Basics-1728574618398.zip'),
(8, 'file_upload_download.zip', 569427, 'application/zip', '2025-05-13 12:24:16', 'https://visualbook.kvmediastudio.cz/uploads/file_upload_download.zip'),
(18, 'file_upload_download.zip', 569427, 'application/zip', '2025-05-14 07:17:29', 'https://visualbook.kvmediastudio.cz/uploads/file_upload_download.zip'),
(27, 'OculusSetup.exe', 4774136, 'application/x-msdownload', '2025-05-14 08:22:33', 'https://visualbook.kvmediastudio.cz/uploads/OculusSetup.exe'),
(28, 'file_upload_download.zip', 569427, 'application/zip', '2025-05-14 10:21:29', 'https://visualbook.kvmediastudio.cz/uploads/file_upload_download.zip'),
(29, 'file_upload_download.zip', 569427, 'application/zip', '2025-05-14 10:21:52', 'https://visualbook.kvmediastudio.cz/uploads/file_upload_download.zip'),
(30, 'file_upload_download.zip', 569427, 'application/zip', '2025-05-14 10:28:07', 'https://visualbook.kvmediastudio.cz/uploads/file_upload_download.zip'),
(31, 'Install_League_of_Legends_euw.exe', 74510024, 'application/x-msdownload', '2025-05-14 10:31:48', 'https://visualbook.kvmediastudio.cz/uploads/Install_League_of_Legends_euw.exe'),
(33, 'ally_icon.png', 1000332, 'image/png', '2025-05-21 08:36:19', 'https://visualbook.kvmediastudio.cz/uploads/ally_icon.png'),
(34, 'ally_icon.png', 1000332, 'image/png', '2025-05-21 08:36:33', 'https://visualbook.kvmediastudio.cz/uploads/ally_icon.png'),
(35, 'brain.gif', 18909, 'image/gif', '2025-05-21 08:37:37', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(36, 'brain.gif', 18909, 'image/gif', '2025-05-21 09:13:07', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(37, 'wallpaper.png', 16985, 'image/png', '2025-05-21 10:47:51', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(38, 'urban_architecture.jpg', 2676953, 'image/jpeg', '2025-05-21 11:20:08', 'https://visualbook.kvmediastudio.cz/uploads/urban_architecture.jpg'),
(39, 'urban_architecture.jpg', 2676953, 'image/jpeg', '2025-05-21 11:41:51', 'https://visualbook.kvmediastudio.cz/uploads/urban_architecture.jpg'),
(40, 'Hlavní_města_(správně).pdf', 698812, 'application/pdf', '2025-05-21 12:17:01', 'https://visualbook.kvmediastudio.cz/uploads/Hlavn%C3%AD_m%C4%9Bsta_%28spr%C3%A1vn%C4%9B%29.pdf'),
(41, 'FCE_Reading_and_Use_of_English_Test_21_Printable_(2024_edition).pdf', 141269, 'application/pdf', '2025-05-21 12:17:32', 'https://visualbook.kvmediastudio.cz/uploads/FCE_Reading_and_Use_of_English_Test_21_Printable_%282024_edition%29.pdf'),
(42, 'wallpaper.png', 16985, 'image/png', '2025-05-21 12:26:20', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(43, 'brain.gif', 18909, 'image/gif', '2025-05-21 12:26:48', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(44, 'Hlavní_města_(správně).pdf', 698812, 'application/pdf', '2025-05-22 07:30:03', 'https://visualbook.kvmediastudio.cz/uploads/Hlavn%C3%AD_m%C4%9Bsta_%28spr%C3%A1vn%C4%9B%29.pdf'),
(45, 'wallpaper.png', 16985, 'image/png', '2025-05-22 10:02:19', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(46, 'Hlavni-mesta-spravne.pdf', 698812, 'application/pdf', '2025-05-22 10:02:40', 'https://visualbook.kvmediastudio.cz/uploads/Hlavni-mesta-spravne.pdf'),
(47, 'urban-architecture.jpeg', 2676953, 'image/jpeg', '2025-05-22 10:06:34', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture.jpeg'),
(48, 'pikachu.gif', 74953, 'image/gif', '2025-05-22 10:06:38', 'https://visualbook.kvmediastudio.cz/uploads/pikachu.gif'),
(49, 'wallpaper.png', 16985, 'image/png', '2025-05-22 11:26:16', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(50, 'brain.gif', 18909, 'image/gif', '2025-05-22 11:29:12', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(51, 'wallpaper.png', 16985, 'image/png', '2025-05-22 11:29:24', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(52, 'knight.gif', 170857, 'image/gif', '2025-05-22 11:29:51', 'https://visualbook.kvmediastudio.cz/uploads/knight.gif'),
(53, 'pikachu.gif', 74953, 'image/gif', '2025-05-22 11:32:04', 'https://visualbook.kvmediastudio.cz/uploads/pikachu.gif'),
(54, 'knight.gif', 170857, 'image/gif', '2025-05-22 11:32:28', 'https://visualbook.kvmediastudio.cz/uploads/knight.gif'),
(55, 'knight.gif', 170857, 'image/gif', '2025-05-22 11:34:18', 'https://visualbook.kvmediastudio.cz/uploads/knight.gif'),
(56, 'brain.gif', 18909, 'image/gif', '2025-05-22 12:19:55', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(57, 'pikachu.gif', 74953, 'image/gif', '2025-05-23 07:28:15', 'https://visualbook.kvmediastudio.cz/uploads/pikachu.gif'),
(58, 'ally-icon.png', 1000332, 'image/png', '2025-05-23 07:53:15', 'https://visualbook.kvmediastudio.cz/uploads/ally-icon.png'),
(59, 'wallpaper.png', 16985, 'image/png', '2025-05-23 08:54:06', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(60, 'wallpaper.png', 16985, 'image/png', '2025-05-23 11:21:44', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(61, 'FCE-Reading-and-Use-of-English-Test-17-Printable-2024-edition.pdf', 161386, 'application/pdf', '2025-05-26 18:26:25', 'https://visualbook.kvmediastudio.cz/uploads/FCE-Reading-and-Use-of-English-Test-17-Printable-2024-edition.pdf'),
(62, 'urban-architecture.jpeg', 2676953, 'image/jpeg', '2025-05-28 08:33:22', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture.jpeg'),
(63, 'wallpaper.png', 16985, 'image/png', '2025-05-28 10:00:24', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(64, 'wallpaper.png', 16985, 'image/png', '2025-05-28 10:25:22', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(65, 'urban-architecture-1.jpeg', 2676953, 'image/jpeg', '2025-05-28 12:06:53', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture-1.jpeg'),
(66, 'wallpaper.png', 16985, 'image/png', '2025-05-28 12:10:49', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(67, 'wallpaper.png', 16985, 'image/png', '2025-05-28 12:43:08', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(68, 'knight.gif', 170857, 'image/gif', '2025-05-28 12:43:08', 'https://visualbook.kvmediastudio.cz/uploads/knight.gif'),
(69, 'pikachu.gif', 74953, 'image/gif', '2025-05-28 12:43:41', 'https://visualbook.kvmediastudio.cz/uploads/pikachu.gif'),
(70, 'knight.gif', 170857, 'image/gif', '2025-05-28 12:43:41', 'https://visualbook.kvmediastudio.cz/uploads/knight.gif'),
(71, 'brain.gif', 18909, 'image/gif', '2025-05-28 12:43:41', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(72, 'wallpaper.png', 16985, 'image/png', '2025-05-28 12:45:27', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(73, 'urban-architecture-1.jpeg', 2676953, 'image/jpeg', '2025-05-28 12:45:27', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture-1.jpeg'),
(74, 'urban-architecture.jpeg', 2676953, 'image/jpeg', '2025-05-28 12:45:27', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture.jpeg'),
(75, 'wallpaper.png', 16985, 'image/png', '2025-05-28 12:46:55', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(76, 'urban-architecture-1.jpeg', 2676953, 'image/jpeg', '2025-05-28 12:46:55', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture-1.jpeg'),
(77, 'urban-architecture.jpeg', 2676953, 'image/jpeg', '2025-05-28 12:46:55', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture.jpeg'),
(78, 'petrzel-zelenina-korenova0802-denik-630-16x9.jpeg', 30776, 'image/jpeg', '2025-05-28 15:37:10', 'https://visualbook.kvmediastudio.cz/uploads/petrzel-zelenina-korenova0802-denik-630-16x9.jpeg'),
(79, '23563-1.jpeg', 35588, 'image/jpeg', '2025-05-28 15:40:18', 'https://visualbook.kvmediastudio.cz/uploads/23563-1.jpeg'),
(80, 'pikachu.gif', 74953, 'image/gif', '2025-05-29 16:31:47', 'https://visualbook.kvmediastudio.cz/uploads/pikachu.gif'),
(81, 'brain.gif', 18909, 'image/gif', '2025-05-29 16:31:47', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(82, 'ally-icon.png', 1000332, 'image/png', '2025-05-30 07:34:58', 'https://visualbook.kvmediastudio.cz/uploads/ally-icon.png'),
(83, 'ally-icon-1.png', 1000332, 'image/png', '2025-05-30 07:34:58', 'https://visualbook.kvmediastudio.cz/uploads/ally-icon-1.png'),
(84, 'brain.gif', 18909, 'image/gif', '2025-05-30 07:34:58', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(85, 'brain.gif', 18909, 'image/gif', '2025-05-30 12:27:35', 'https://visualbook.kvmediastudio.cz/uploads/brain.gif'),
(86, 'FCE-Reading-and-Use-of-English-Test-20-Printable-2024-edition.pdf', 167328, 'application/pdf', '2025-05-30 15:42:42', 'https://visualbook.kvmediastudio.cz/uploads/FCE-Reading-and-Use-of-English-Test-20-Printable-2024-edition.pdf'),
(87, 'Screenshot-2025-05-30-21-26-40-450-cz.mafra.jizdnirady.jpeg', 884053, 'image/jpeg', '2025-05-31 15:25:45', 'https://visualbook.kvmediastudio.cz/uploads/Screenshot-2025-05-30-21-26-40-450-cz.mafra.jizdnirady.jpeg'),
(88, 'IMG-20250530-090357.jpeg', 4062530, 'image/jpeg', '2025-05-31 15:25:45', 'https://visualbook.kvmediastudio.cz/uploads/IMG-20250530-090357.jpeg'),
(89, 'IMG-20250528-WA0000.jpeg', 512735, 'image/jpeg', '2025-05-31 15:25:45', 'https://visualbook.kvmediastudio.cz/uploads/IMG-20250528-WA0000.jpeg'),
(90, 'urban-architecture.jpeg', 2676953, 'image/jpeg', '2025-06-02 06:57:50', 'https://visualbook.kvmediastudio.cz/uploads/urban-architecture.jpeg'),
(91, 'wallpaper.png', 16985, 'image/png', '2025-06-02 06:57:50', 'https://visualbook.kvmediastudio.cz/uploads/wallpaper.png'),
(92, 'win-sp-gac.zip', 42060044, 'application/zip', '2025-06-06 07:16:06', 'https://visualbook.kvmediastudio.cz/uploads/win-sp-gac.zip'),
(93, 'win.zip', 42060044, 'application/zip', '2025-06-06 07:17:53', 'https://visualbook.kvmediastudio.cz/uploads/win.zip'),
(94, 'sp-ivalo.zip', 64401752, 'application/zip', '2025-06-19 15:21:55', 'https://visualbook.kvmediastudio.cz/uploads/sp-ivalo.zip');

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `projects` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

--
-- Dumping data for table `projects`
--

INSERT INTO `projects` (`id`, `name`, `note`, `user_id`, `created`) VALUES
(3, 'google', 'searching platform\r\n', 2, '2025-05-25 17:10:24'),
(9, 'jonas', 'jonas has a company\r\n', 26, '2025-05-26 10:51:04'),
(10, 'e', 'e', 2, '2025-05-26 13:30:43'),
(11, 'skibidi_project', 'New project from your skibidi toilet', 39, '2025-05-26 13:30:53'),
(13, 'The E project', 'E', 2, '2025-05-26 13:35:12'),
(15, 'skibidi_project', 'another skibidi project\r\n\r\n', 14, '2025-05-26 14:19:55'),
(16, 'project name', 'note is optional\r\n', 26, '2025-05-27 12:10:08'),
(17, 'yet another E project', 'E and E', 2, '2025-05-27 12:17:44'),
(19, 'm', 'm', 48, '2025-05-27 14:36:43'),
(20, 'L', 'The massive L', 50, '2025-05-28 11:59:14'),
(22, 'smrdim', 'julian stinks', 51, '2025-05-28 17:36:15'),
(24, 'i', ' ', 53, '2025-05-30 09:48:47'),
(25, 'g', ' ', 53, '2025-05-30 09:52:50'),
(26, 'g', '', 53, '2025-05-30 09:54:03'),
(27, 'a', '', 53, '2025-05-30 09:54:07'),
(28, 'e', 'e', 26, '2025-05-30 11:23:44');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `firstname` varchar(30) NOT NULL,
  `lastname` varchar(30) NOT NULL,
  `pwd` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT curtime(),
  `role` varchar(20) NOT NULL DEFAULT 'user'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `firstname`, `lastname`, `pwd`, `email`, `created_at`, `role`) VALUES
(1, 'Daniel', 'Baier', '$2y$12$ITdGw98JNt1MyqaYSqUl8uf3q2zsvFkl4eoQG5nd4TROPu7/f10Au', '<EMAIL>', '2025-05-07 10:00:13', 'user'),
(2, 'e', 'e', '$2y$12$8xX/qKVUDE1agLFoCTxQn./eGzaJdF./zYkIk9r.jMOMwZ7sTYZ9S', 'e@e.e', '2025-05-07 10:02:01', 'admin'),
(3, 'Daniel', 'B', '$2y$12$of1Hk3uQAFd7LHndU3RseegDanI.tTF.skQ81r84KV34vQ5xhXIb6', '<EMAIL>', '2025-05-07 11:53:12', 'user'),
(4, 'Daniel', 'Thrasher', '$2y$12$9xRnmlxaYrhxWyojLIbKiurBJRjPAGCmlij7/LnoU3fHZCxzh4PsW', '<EMAIL>', '2025-05-07 12:15:32', 'user'),
(5, 'Daniel', 'Tshtoen', '$2y$12$8SWFyPHNgcXtOP3Zs7daJ.mDlXytqMb.A/QtOisU8BC8Xz3LXSJwO', '<EMAIL>', '2025-05-07 14:04:01', 'user'),
(6, 'Jonas', 'Kyml', '$2y$12$Gy8TuaaBO3b1/CvHgBMwHunF5D.MWjmTi1UrCTJlpW25IpNx2338y', '<EMAIL>', '2025-05-12 10:03:18', 'user'),
(7, 'F', 'K', '$2y$12$39y6SUkDEQDfC4ylZVVvWeenXDYNIXh6ekgHSxKX1ibGCWY3AE4ba', '<EMAIL>', '2025-05-12 10:36:22', 'user'),
(8, 'J', 'K', '$2y$12$jmBWHydR9zmexAnf7A11Y./VghuTqLGnHsFnOS7x0amatThxiFnDa', 'j.k@jk.k', '2025-05-12 10:39:21', 'user'),
(9, 'Mobile', 'Phone', '$2y$12$mbfZy6sYrf829o8g9hdoe.TkWpDaoy1bo9fT4ishztPKVdLWuvAYW', '<EMAIL>', '2025-05-12 10:44:47', 'user'),
(10, 'adam', 'shoten', '$2y$12$cKiSX.iq9uCh.M3Qk/wUgeE.tJX2bG2.4O/7ymI02uHDVc/43QHY.', 'adam/<EMAIL>', '2025-05-12 11:10:18', 'user'),
(11, 'aa', 'aaa', '$2y$12$5Z0YTlhiDoNImmKoa22cMehfWBqhctrRDeLZA30wJY9nDWtrORB82', '<EMAIL>', '2025-05-12 11:15:06', 'user'),
(12, 'what', 'is', '$2y$12$EZx8acfADsmyyhzDKD9Che3WnFQxjlYPv6MOWgWPpenMXpgA3KpOK', '<EMAIL>', '2025-05-12 12:40:44', 'user'),
(13, 'John', 'Doe', '$2y$12$3SxxcN7LkhCBZNVucPVHT.aNtLLkF7GQFM5S0BW1XZdJvf0WkfCDe', '<EMAIL>', '2025-05-12 13:11:15', 'user'),
(14, 'skibidi', 'toilet', '$2y$12$4Ys/kdsidqn5zcx8UnN6VucXA1IhsPhNLrIS/hf2x3cClFT1xbnSC', '<EMAIL>', '2025-05-12 13:15:59', 'admin'),
(15, 'Petr', 'Knap', '$2y$12$F/YGC13.WzyLO1ksoLkd6OccXxkJ0IQmALqaTmdPtjDnROe3vsy1m', '<EMAIL>', '2025-05-14 12:22:29', 'user'),
(16, 'a', 'a', '$2y$12$khtoCWUZ1utg5uaPXHjNMOjjAl8aTDOp1p5ZESv3307rOnRzCw9d.', 'a@a.a', '2025-05-19 10:03:00', 'user'),
(17, 'o', 'o', '$2y$12$EibMhzHUpqcDngSwTIY90u8xupK10c8MDnpFyq908ofXXh8NoWcOS', 'o@o.o', '2025-05-19 10:04:13', 'user'),
(18, 'u', 'u', '$2y$10$xdo9zIPigB3ogLFjy4AMa.TBrB/DaoXMfjhdwHt5JMF8N2cq4aPSy', 'u@u.u', '2025-05-19 10:51:35', 'user'),
(19, 'Hello', 'There', '$2y$10$Xz8Ce965y0FRg7K7uRJaOex1I.DJpsB70yq0Y.eDO8BGDHDDzYo5q', '<EMAIL>', '2025-05-19 11:17:07', 'user'),
(20, 'a a', 'a', '$2y$10$bYPSrbyEbZhfa/m7Bd0C/uuMQmuhEf8IZPj4HV2J.OHCHAVMSpjEa', 'aa@a.a', '2025-05-19 11:22:22', 'user'),
(22, 'u u', 'u u', '$2y$10$ON.jKgqwy65XdADa3BjBXu6IwRTXOhQCdPlAZN/XP2GfC3Hpfsv52', '<EMAIL>', '2025-05-19 11:49:11', 'user'),
(23, 'hello', 'world', '$2y$10$5/r1fPXYBGLVpnOFwuqUSewcZB9XgXqtwh0k0dEprWwQc8fE2aE2m', '<EMAIL>', '2025-05-19 13:04:20', 'user'),
(24, 'ndwyk', 'dwtnoy', '$2y$10$7M6.uSpcY6RFHTsNFKSJ6.lpDgR0oXE.jtRMnK79rUp97OJxJ1CWO', 'soetn', '2025-05-19 14:56:32', 'user'),
(25, 'Hi', 'Jack', '$2y$10$2qRc/xvDTBFdzId6zpLi6u.KEJassFivTUMm3ghZRKNBvLiCh0yZ6', '<EMAIL>', '2025-05-20 09:41:45', 'user'),
(26, 'jonas', 'kyml', '$2y$10$UrhBR0zzuCENRcBqORITAu8NLt2SNkYgWVf2iz2xpjIckiSuhUIcy', '<EMAIL>', '2025-05-21 13:54:53', 'user'),
(27, 'ah', 'shit', '$2y$10$VDGWo4M9htIApzWT7xKOK.yystRFKHsXCFWzd7EYzL/pei3VbrPSq', '<EMAIL>', '2025-05-22 14:22:39', 'user'),
(28, 'mobile', 'two', '$2y$10$ZUd1TIYathSoi1F6Q4Q6seJrv3J8RO/4e9uE0kFYie147SNzbvA52', '<EMAIL>', '2025-05-23 08:19:42', 'user'),
(29, 'come', 'on', '$2y$10$AATH.gOwVb382vYtQTW18e50.HluLGKybEo/M1C46I7y7liFFmfPq', '<EMAIL>', '2025-05-23 09:29:09', 'user'),
(30, 'jack', 'hi', '$2y$10$FGUNRV2mJQAJ2ckVveW05.iUKsb6Ymdl0MN1GGiLD.xEDth54KVKi', '<EMAIL>', '2025-05-23 09:44:42', 'user'),
(31, 'come', 'on', '$2y$10$faT5LiOLSeeDEsi/PSRBbu3Hz2wwHpMfrJ3mhEmNkbJ6GH86vafpm', '<EMAIL>', '2025-05-23 09:56:11', 'user'),
(32, 'just', 'work', '$2y$10$mSZcZ3v7Y6FROnjcYhChYutUQNbsGYz1cI40ZcaR8tyrkxtM6Fbam', '<EMAIL>', '2025-05-23 09:57:55', 'user'),
(33, 'stoen', 'hsotugn', '$2y$10$pn4YwlbtfqR5ybo5VDN2h.smwig2.gagiC24WE5uapkCWT.QkQMhK', '<EMAIL>', '2025-05-23 10:53:25', 'user'),
(34, 'Another', 'Try', '$2y$10$f1andohKbgY5Y43Fq/6X4eICnBJcMMeo4KRMhrpVYTi0Tux43qS9S', '<EMAIL>', '2025-05-26 10:49:20', 'user'),
(35, 'what', 'if', '$2y$10$6onaM9.pGK/A2v1UeyV2bO9AAJQZBYSphBkHGzTHuMh1/vDHWtrum', '<EMAIL>', '2025-05-26 12:09:18', 'user'),
(36, 't', 't', '$2y$10$dpFV91HZbQM9iOGVbEcTDu5TLBTXqQJ4nj9nu66E4t4I2FKZDSeV6', 't@t.t', '2025-05-26 12:32:59', 'user'),
(37, 'Hello', 'There', '$2y$10$yEu3./1dZbIdCWfWpZAp2upD46Drr0wUFrw0SCq.gK/XUBya5gVVi', '<EMAIL>', '2025-05-26 13:19:38', 'user'),
(38, 'Hey', 'Dog', '$2y$10$LbUc0mD2./DdgidKlfd4j./Ch5qxrgEiacQOzJuSxP/jmSNoiAfeu', '<EMAIL>', '2025-05-26 13:20:10', 'user'),
(39, 'oahstne', 'ashtoek', '$2y$10$bFTdnyvDKmP3mLLKbP4B7uwa7emBTHRTZiDAXebzhZfFmw3SFh7ba', 't@a.t', '2025-05-26 13:22:51', 'user'),
(40, 'try', 'catch', '$2y$10$wFOyRa25ioacJC5hLR5ctuCk7tUs68zh/5XBWfg/prPA/sH.Z.pQ6', 'method', '2025-05-27 11:52:35', 'user'),
(41, 'a', 'a', '$2y$10$AAB5dgYK8cVoiqTn36twfe.xVvviPufbd0FXbXYDbCo7Lx4n70drG', 'a', '2025-05-27 11:53:49', 'user'),
(42, 'b', 'b', '$2y$10$VHBSMaD7qKqnnRBIVW5LzOIZZ97UTz20/gBgfAb.bM0FidXe5Hrw.', 'b@b.b', '2025-05-27 12:01:09', 'user'),
(43, 'c', 'c', '$2y$10$lvdOwOvev1qTz0umF9kyiu19SVr8eRoZKp8FgttGLAw/bbGkcxtZq', 'c@c.c', '2025-05-27 12:17:10', 'user'),
(44, 'd', 'd', '$2y$10$u9BE12xy6Si5VygQCiV.vObwUOX34etU8f9xBDuZ7KuHDKuUZZv0m', 'd@d.d', '2025-05-27 13:26:02', 'user'),
(45, 'f', 'f', '$2y$10$ErFX/RpAyqoJEtKRMGbuNu1W0unlEIZsRIRhYmIkUTJ.lgkeIOAfq', 'f@f.f', '2025-05-27 13:29:12', 'user'),
(46, 'g', 'g', '$2y$10$dQQuvQyTA3QL4rpTDurm.el2Hs.lO2k0z5eFTjKEjJ6KApjGnGqri', 'g@g.g', '2025-05-27 13:31:20', 'user'),
(47, 'gg', 'gg', '$2y$10$9rd6xAHEMrJFflctSmNK5OlUcbnFox4XbFx2RSg7G/WP7xOE7jbDW', '<EMAIL>', '2025-05-27 13:35:59', 'user'),
(48, 'm', 'm', '$2y$10$hlZkKQdnTQEcf7M67RUgMePqZJtq7qHufhFOhjlZc2xp2.1yvWF0a', 'm@m.m', '2025-05-27 14:36:34', 'user'),
(49, 'h', 'h', '$2y$10$i7Z6dZajvahibk2I1X00..9r2nOQ9FHSF0TLSRwziVxh.HWPJKmBe', 'h@h.h', '2025-05-28 09:28:42', 'user'),
(50, 'l', 'l', '$2y$10$eVuEsWjKqF.2gtSukic6kuJrUen02QKjcSGEKQaKwYVKC610QK0vG', 'l@l.l', '2025-05-28 11:58:53', 'user'),
(51, 'julian', 'kyml', '$2y$10$0d7y6K240mgEJPRRLpe.Ou1AZs92RKGK3aZWZlL5azDtaN1TMTa1.', '<EMAIL>', '2025-05-28 17:35:32', 'user'),
(52, 'Saja', 'Doe', '$2y$10$0hX27HPhCCwsmOnUmEKUXuTu3RwimlRDKUCAkEaNcfja1J0K1/6Xu', '<EMAIL>', '2025-05-29 18:27:44', 'user'),
(53, 'n', 'n', '$2y$10$io5zqZ1dAUxylYCGu6zO0.lMxMftYh/ZLP8DpwmxT2UAYyS31X006', 'n@n.n', '2025-05-30 09:35:38', 'user'),
(54, 'k', 'k', '$2y$10$4A45GXkGaFl1k7BK7gDKBe5AGI88yRmQjCLOUR7OtOBoQqa./wlCy', 'k@k.k', '2025-06-02 08:54:30', 'user'),
(55, 'p', 'p', '$2y$10$sTMvIGxkviZEnfVACzRQTeXxAFiQ/itnmq65FNJTBU31wshn1aQYi', 'p@p.p', '2025-06-07 22:40:57', 'user');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=95;

--
-- AUTO_INCREMENT for table `projects`
--
ALTER TABLE `projects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
